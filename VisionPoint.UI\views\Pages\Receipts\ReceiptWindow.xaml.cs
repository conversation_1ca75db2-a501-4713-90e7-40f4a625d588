using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.Reports.Reciept;
using VisionPoint.UI.ViewModels;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Receipts
{
    /// <summary>
    /// Interaction logic for ReceiptWindow.xaml
    /// </summary>
    public partial class ReceiptWindow : Window
    {
        private ClientService _clientService;
        private UserService _userService;
        private TreasuryService _treasuryService;
        private PurchaseService _purchaseService;
        private SaleService _saleService;
        private ReceiptService _receiptService;
        private ExpenseService _expenseService;
        private WarehouseService _warehouseService;

        private Receipt _currentReceipt;

        // قوائم البيانات
        private List<Client> _clients;
        private List<User> _employees;
        private List<Models.Treasury> _treasuries;
        private List<Financial> _financials;
        private List<Expense> _expenses;
        private List<Warehouse> _warehouses;
        private bool _isEditMode = false;
        private int _receiptId = 0;

        // متغيرات لحفظ البيانات المحملة
        private Purchase _loadedPurchase;
        private Sale _loadedSale;

        public ReceiptWindow()
        {
            InitializeComponent();
            InitializeServices();

            // تهيئة البيانات
            _currentReceipt = new Receipt();

            // ربط أحداث حقل رقم الفاتورة
            txtInvoiceNo.KeyDown += TxtInvoiceNo_KeyDown;
            txtInvoiceNo.TextChanged += txtInvoiceNo_TextChanged;
        }

        public ReceiptWindow(int receiptId) : this()
        {
            _receiptId = receiptId;
            _isEditMode = receiptId > 0;
        }

        private void InitializeServices()
        {
            // الحصول على الخدمات
            _clientService = ServiceLocator.GetService<ClientService>();
            _userService = ServiceLocator.GetService<UserService>();
            _treasuryService = ServiceLocator.GetService<TreasuryService>();
            _purchaseService = ServiceLocator.GetService<PurchaseService>();
            _saleService = ServiceLocator.GetService<SaleService>();
            _receiptService = ServiceLocator.GetService<ReceiptService>();
            _expenseService = ServiceLocator.GetService<ExpenseService>();
            _warehouseService = ServiceLocator.GetService<WarehouseService>();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // ضبط تاريخ اليوم كقيمة افتراضية
            DtpGeneralExpireOn.SelectedDate = DateTime.Today;

            // التحقق من صلاحية تغيير التاريخ
            bool canChangeDate = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeReceiptDateRole");
            DtpGeneralExpireOn.IsEnabled = canChangeDate;

            // التحقق من صلاحيات المستخدم للتعديل
            bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditReceiptRole");

            await LoadData();

            // إذا كان في وضع التعديل، تحميل بيانات الإيصال
            if (_isEditMode && _receiptId > 0)
            {
                await LoadReceiptForEdit(_receiptId);
                this.Title = "تعديل الإيصال";

                // تفعيل زر الطباعة للإيصالات الموجودة
                btnPrint.IsEnabled = true;
                btnPrint.Opacity = 1.0;
            }
            else
            {
                this.Title = "إضافة إيصال جديد";

                // تعطيل زر الطباعة للإيصالات الجديدة حتى يتم الحفظ
                btnPrint.IsEnabled = false;
                btnPrint.Opacity = 0.5;
                cmbType.SelectedIndex = 0;
            }
        }

        private async Task LoadData()
        {
            _isLoadingData = true;
            try
            {
                // تحميل البيانات الأساسية فقط في البداية
                txtValue.Text = "0";

                // تحميل المخازن والأنواع المالية فقط
                var allWarehouses = await _warehouseService.GetAllWarehousesAsync();
                _financials = await _receiptService.GetFinancials();

                // إدارة صلاحيات المخازن
                bool canChangeWarehouse = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeWarehouseRole");

                // إعداد قائمة المخازن حسب الصلاحيات
                _warehouses = new List<Warehouse>();

                if (canChangeWarehouse)
                {
                    // إضافة خيار "جميع المخازن" للمستخدمين المخولين
                    _warehouses.Add(new Warehouse { Id = -1, Name = "جميع المخازن" });
                    _warehouses.AddRange(allWarehouses);
                }
                else
                {
                    // إضافة جميع المخازن للمستخدمين العاديين لكن سيتم تعطيل القائمة
                    _warehouses.AddRange(allWarehouses);
                }

                // تكوين كومبوبوكس المخازن
                cmbWarehouse.ItemsSource = _warehouses;
                cmbWarehouse.DisplayMemberPath = "Name";
                cmbWarehouse.SelectedValuePath = "Id";

                if (CurrentUser.WarehouseId.HasValue)
                {
                    // تحديد المخزن الافتراضي للمستخدم الحالي
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;

                    // إذا لم يكن لديه صلاحية تغيير المخزن، نعطل القائمة
                    if (!canChangeWarehouse)
                    {
                        cmbWarehouse.IsEnabled = false;
                    }
                }
                else
                {
                    // إذا لم يكن له مخزن محدد، يمكنه اختيار أي مخزن
                    cmbWarehouse.IsEnabled = true;

                    // اختيار "جميع المخازن" كافتراضي للمستخدمين المخولين
                    if (canChangeWarehouse)
                    {
                        cmbWarehouse.SelectedValue = -1;
                    }
                }

                // تكوين كومبوبوكس نوع العملية المالية
                cmbType.ItemsSource = _financials;
                cmbType.DisplayMemberPath = "Name";
                cmbType.SelectedValuePath = "Id";

                // لا نحمل أي بيانات أخرى - سيتم تحميلها عند اختيار النوع
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ في تحميل البيانات", true);
            }
            finally
            {
                _isLoadingData = false;
            }
        }

        private bool _isLoadingData = false;

        /// <summary>
        /// تحميل طرق الدفع حسب المخزن المختار
        /// </summary>
        private async Task LoadTreasuries()
        {
            try
            {
                // الحصول على المخزن المختار
                int? selectedWarehouseId = null;
                if (cmbWarehouse.SelectedValue != null && (int)cmbWarehouse.SelectedValue > 0)
                {
                    selectedWarehouseId = (int)cmbWarehouse.SelectedValue;
                }

                // تحميل طرق الدفع حسب المخزن
                if (selectedWarehouseId.HasValue)
                {
                    _treasuries = await _treasuryService.GetTreasuriesByWarehouseAsync(selectedWarehouseId.Value);
                }
                else
                {
                    // إذا كان "جميع المخازن" أو لا يوجد مخزن محدد، تحميل جميع طرق الدفع
                    _treasuries = await _treasuryService.GetAllTreasuriesAsync();
                }

                // التحقق من أن النافذة لا تزال مفتوحة
                if (cmbTreasury != null)
                {
                    cmbTreasury.ItemsSource = _treasuries;
                    cmbTreasury.DisplayMemberPath = "Name";
                    cmbTreasury.SelectedValuePath = "Id";
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل طرق الدفع: {ex.Message}", "خطأ");
            }
        }

        /// <summary>
        /// تحميل العملاء حسب المخزن المختار
        /// </summary>
        private async Task LoadClients()
        {
            try
            {
                // الحصول على المخزن المختار
                int? selectedWarehouseId = null;
                if (cmbWarehouse.SelectedValue != null && (int)cmbWarehouse.SelectedValue > 0)
                {
                    selectedWarehouseId = (int)cmbWarehouse.SelectedValue;
                }

                // تحميل العملاء حسب المخزن
                if (selectedWarehouseId.HasValue)
                {
                    _clients = await _clientService.GetClientsByWarehouseAsync(selectedWarehouseId.Value);
                }
                else
                {
                    // إذا كان "جميع المخازن" أو لا يوجد مخزن محدد، تحميل جميع العملاء
                    _clients = await _clientService.GetAllClientsAsync();
                }

                // التحقق من أن النافذة لا تزال مفتوحة
                if (cmbClient != null)
                {
                    cmbClient.ItemsSource = _clients;
                    cmbClient.DisplayMemberPath = "Name";
                    cmbClient.SelectedValuePath = "Id";
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل العملاء: {ex.Message}", "خطأ");
            }
        }

        /// <summary>
        /// تحميل الموظفين حسب المخزن المختار
        /// </summary>
        private async Task LoadEmployees()
        {
            try
            {
                // الحصول على المخزن المختار
                int? selectedWarehouseId = null;
                if (cmbWarehouse.SelectedValue != null && (int)cmbWarehouse.SelectedValue > 0)
                {
                    selectedWarehouseId = (int)cmbWarehouse.SelectedValue;
                }

                // تحميل الموظفين حسب المخزن
                if (selectedWarehouseId.HasValue)
                {
                    _employees = await _userService.GetUsersByWarehouseAsync(selectedWarehouseId.Value);
                }
                else
                {
                    // إذا كان "جميع المخازن" أو لا يوجد مخزن محدد، تحميل جميع الموظفين
                    _employees = await _userService.GetAllUsersAsync();
                }

                // التحقق من أن النافذة لا تزال مفتوحة
                if (cmbEmploye != null)
                {
                    cmbEmploye.ItemsSource = _employees;
                    cmbEmploye.DisplayMemberPath = "Name";
                    cmbEmploye.SelectedValuePath = "Id";
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل الموظفين: {ex.Message}", "خطأ");
            }
        }

        /// <summary>
        /// تحميل المصروفات
        /// </summary>
        private async Task LoadExpenses()
        {
            try
            {
                _expenses = await _expenseService.GetAllExpensesAsync();

                // التحقق من أن النافذة لا تزال مفتوحة
                if (cmbExpense != null)
                {
                    cmbExpense.ItemsSource = _expenses;
                    cmbExpense.DisplayMemberPath = "Name";
                    cmbExpense.SelectedValuePath = "Id";
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المصروفات: {ex.Message}", "خطأ");
            }
        }

        /// <summary>
        /// تحميل جميع طرق الدفع للتحويل (بدون فلترة بالمخزن)
        /// </summary>
        private async Task LoadAllTreasuriesForTransfer()
        {
            try
            {
                _treasuries = await _treasuryService.GetAllTreasuriesAsync();

                // التحقق من أن النافذة لا تزال مفتوحة
                if (cmbSourceTreasury != null && cmbTargetTreasury != null)
                {
                    cmbSourceTreasury.ItemsSource = _treasuries;
                    cmbSourceTreasury.DisplayMemberPath = "Name";
                    cmbSourceTreasury.SelectedValuePath = "Id";

                    cmbTargetTreasury.ItemsSource = _treasuries;
                    cmbTargetTreasury.DisplayMemberPath = "Name";
                    cmbTargetTreasury.SelectedValuePath = "Id";
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل طرق الدفع: {ex.Message}", "خطأ");
            }
        }



        /// <summary>
        /// تحميل البيانات المطلوبة حسب نوع الإيصال المختار
        /// </summary>
        private async Task LoadDataBasedOnReceiptType()
        {
            if (cmbType.SelectedValue == null) return;

            byte financialId = (byte)cmbType.SelectedValue;

            try
            {
                switch (financialId)
                {
                    case (byte)FinancialId.Client:
                    case (byte)FinancialId.Sale:
                    case (byte)FinancialId.Purchase:
                    case (byte)FinancialId.OpeningBalanceForClient:
                        // تحميل العملاء وطرق الدفع
                        await LoadClients();
                        if (financialId != (byte)FinancialId.OpeningBalanceForClient)
                        {
                            await LoadTreasuries();
                        }
                        break;

                    case (byte)FinancialId.Employee:
                    case (byte)FinancialId.SalaryPayment:
                        // تحميل الموظفين وطرق الدفع
                        await LoadEmployees();
                        await LoadTreasuries();
                        break;

                    case (byte)FinancialId.OpeningBalanceForEmployee:
                        // تحميل الموظفين فقط
                        await LoadEmployees();
                        break;

                    case (byte)FinancialId.Expense:
                        // تحميل المصروفات وطرق الدفع
                        await LoadExpenses();
                        await LoadTreasuries();
                        break;

                    case (byte)FinancialId.Deposit:
                    case (byte)FinancialId.Withdrawal:
                        // تحميل طرق الدفع فقط
                        await LoadTreasuries();
                        break;

                    case (byte)FinancialId.Transfer:
                        // تحميل جميع طرق الدفع للتحويل
                        await LoadAllTreasuriesForTransfer();
                        break;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
            }
        }



        private async Task LoadReceiptForEdit(int receiptId)
        {
            _isLoadingData = true;
            try
            {
                var receipt = await _receiptService.GetReceiptById(receiptId);
                if (receipt != null)
                {
                    _currentReceipt = receipt;



                    // تعبئة الحقول بالبيانات
                    txtReceiptNo.Text = receipt.ReceiptNumber;
                    txtValue.Text = FormatDecimalValue(receipt.Value);
                    txtStatment.Text = receipt.Statement;
                    DtpGeneralExpireOn.SelectedDate = receipt.Date;

                    // تحديد نوع الإيصال أولاً
                    if (receipt.FinancialId.HasValue)
                    {
                        cmbType.SelectedValue = receipt.FinancialId.Value;
                        // تحميل البيانات المطلوبة حسب النوع المختار
                        await LoadDataBasedOnReceiptType();

                        // تكوين واجهة المستخدم حسب النوع
                        await ConfigureUIByType();
                    }

                    // تحديد المخزن
                    if (receipt.WarehouseId.HasValue)
                    {
                        cmbWarehouse.SelectedValue = receipt.WarehouseId.Value;
                    }

                    // تحديد اتجاه العملية
                    if (receipt.IsExchange.HasValue)
                    {
                        if (receipt.IsExchange == true)
                        {
                            RdbCatch.IsChecked = true;
                        }
                        else
                        {
                            RdbExchange.IsChecked = true;
                        }
                    }

                    // تحديد العميل إذا وجد
                    if (receipt.ClientId.HasValue)
                    {
                        cmbClient.SelectedValue = receipt.ClientId.Value;
                    }

                    // تحديد الموظف إذا وجد
                    if (receipt.EmployeeId.HasValue)
                    {
                        cmbEmploye.SelectedValue = receipt.EmployeeId.Value;
                    }

                    // تحديد المصروف إذا وجد
                    if (receipt.ExpenseId.HasValue)
                    {
                        cmbExpense.SelectedValue = receipt.ExpenseId.Value;
                    }

                    // تحديد طريقة الدفع إذا وجدت
                    if (receipt.TreasuryId.HasValue)
                    {
                        cmbTreasury.SelectedValue = receipt.TreasuryId.Value;
                    }

                    // تحديد طرق الدفع للتحويل إذا وجدت
                    if (receipt.SourceTreasuryId.HasValue)
                    {
                        cmbSourceTreasury.SelectedValue = receipt.SourceTreasuryId.Value;
                    }

                    if (receipt.TargetTreasuryId.HasValue)
                    {
                        cmbTargetTreasury.SelectedValue = receipt.TargetTreasuryId.Value;
                    }

                    // تحديد رقم الفاتورة إذا وجد
                    if (receipt.PurchaseId.HasValue || receipt.SaleId.HasValue)
                    {
                        if (receipt.PurchaseId.HasValue)
                        {
                            var purchaseViewModel = await _purchaseService.GetPurchaseByIdAsync(receipt.PurchaseId.Value);
                            if (purchaseViewModel != null)
                            {
                                txtInvoiceNo.Text = purchaseViewModel.Purchase.InvoiceNo.ToString();
                                _loadedPurchase = purchaseViewModel.Purchase;
                            }
                        }
                        else if (receipt.SaleId.HasValue)
                        {
                            var saleViewModel = await _saleService.GetSaleByIdAsync(receipt.SaleId.Value);
                            if (saleViewModel != null)
                            {
                                txtInvoiceNo.Text = saleViewModel.Sale.InvoiceNo.ToString();
                                _loadedSale = saleViewModel.Sale;
                            }
                        }

                        // القيود تم تطبيقها بالفعل في ConfigureUIByType
                    }
                }
                else
                {
                    ErrorBox.Show("لم يتم العثور على الإيصال المطلوب", "خطأ");
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات الإيصال: {ex.Message}", "خطأ");
                this.Close();
            }
            finally
            {
                _isLoadingData = false;
            }
        }

        // أحداث التحكم
        private async void cmbType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تجنب التنفيذ أثناء التحميل الأولي
            if (_isLoadingData) return;

            // تحميل البيانات المطلوبة حسب النوع المختار
            await LoadDataBasedOnReceiptType();

            // تكوين واجهة المستخدم حسب النوع
            await ConfigureUIByType();

            // تنظيف الحقول المتعلقة بالفواتير عند تغيير النوع
            if (cmbType.SelectedItem is Financial typeFinancial)
            {
                if (typeFinancial.Id != (byte)FinancialId.Purchase && typeFinancial.Id != (byte)FinancialId.Sale)
                {
                    txtReceiptNo.Text = string.Empty;
                    txtValue.Text = "0";
                    txtStatment.Text = string.Empty;
                    txtInvoiceNo.Text = string.Empty;

                    // إعادة تعيين التاريخ
                    DtpGeneralExpireOn.SelectedDate = DateTime.Today;

                    // إعادة تعيين القوائم المنسدلة
                    cmbClient.SelectedValue = null;
                    cmbEmploye.SelectedValue = null;
                    cmbExpense.SelectedValue = null;
                    cmbTreasury.SelectedValue = null;
                    cmbSourceTreasury.SelectedValue = null;
                    cmbTargetTreasury.SelectedValue = null;

                    // إعادة تعيين أزرار الراديو
                    RdbExchange.IsChecked = true;
                    RdbCatch.IsChecked = false;

                    // إعادة تعيين المتغيرات
                    _currentReceipt = null;
                    _loadedPurchase = null;
                    _loadedSale = null;
                    _isEditMode = false;
                    _receiptId = 0;
                    cmbClient.IsEnabled = true;
                }
            }
        }

        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تجنب التنفيذ أثناء التحميل الأولي
            if (_isLoadingData) return;

            // تحميل البيانات حسب نوع الإيصال المختار
            await LoadDataBasedOnReceiptType();
        }

        private void cmbClient_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // التحقق من وجود رقم فاتورة عند اختيار عميل لفواتير المبيعات/المشتريات
            if (cmbType.SelectedValue != null && cmbClient.SelectedValue != null && cmbClient.IsEnabled)
            {
                byte financialId = (byte)cmbType.SelectedValue;
                if ((financialId == (byte)FinancialId.Sale || financialId == (byte)FinancialId.Purchase)
                    && !string.IsNullOrWhiteSpace(txtInvoiceNo.Text))
                {
                    ErrorBox.Show("لا يمكن اختيار عميل عند وجود رقم فاتورة محدد. يرجى مسح رقم الفاتورة أولاً.", "تعارض في البيانات", false);
                    cmbClient.SelectedValue = null;
                }
            }
        }

        private void cmbEmploye_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        }

        private void cmbExpense_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        }

        private void cmbTreasury_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        }

        private void cmbSourceTreasury_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        }

        private void cmbTargetTreasury_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        }

        private void RdbExchange_Checked(object sender, RoutedEventArgs e)
        {
            // قبض - يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        }

        private void RdbCatch_Checked(object sender, RoutedEventArgs e)
        {
            // صرف - يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        }



        private async void TxtInvoiceNo_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                await SearchInvoice();
            }
        }

        private void txtInvoiceNo_TextChanged(object sender, TextChangedEventArgs e)
        {
            // التحقق من وجود عميل مختار عند كتابة رقم فاتورة
            if (!string.IsNullOrWhiteSpace(txtInvoiceNo.Text) && cmbClient.SelectedValue != null && cmbClient.IsEnabled)
            {
                if (cmbType.SelectedValue != null)
                {
                    byte financialId = (byte)cmbType.SelectedValue;
                    if (financialId == (byte)FinancialId.Sale || financialId == (byte)FinancialId.Purchase)
                    {
                        ErrorBox.Show("لا يمكن إدخال رقم فاتورة عند وجود عميل مختار. يرجى إلغاء اختيار العميل أولاً.", "تعارض في البيانات", false);
                        txtInvoiceNo.Text = string.Empty;
                    }
                }
            }
        }

        private async Task SearchInvoice()
        {
            try
            {
                if (string.IsNullOrEmpty(txtInvoiceNo.Text) || !int.TryParse(txtInvoiceNo.Text, out int invoiceNo))
                {
                    return;
                }

                if (cmbType.SelectedValue == null)
                {
                    ErrorBox.Show("الرجاء اختيار نوع الإيصال أولاً", "تنبيه", false);
                    return;
                }

                byte financialId = (byte)cmbType.SelectedValue;

                // البحث حسب نوع الإيصال
                if (financialId == (byte)FinancialId.Purchase)
                {
                    // البحث في المشتريات
                    var purchaseViewModel = await _purchaseService.GetPurchaseByInvoiceNoAsync(invoiceNo);
                    if (purchaseViewModel != null)
                    {
                        _loadedPurchase = purchaseViewModel.Purchase;
                        // تعبئة بيانات العميل والمبلغ
                        if (purchaseViewModel.Purchase.ClientId > 0)
                        {
                            cmbClient.SelectedValue = purchaseViewModel.Purchase.ClientId;
                            cmbClient.IsEnabled = false; // تعطيل قائمة العملاء عند تحديد فاتورة
                        }

                        // حساب المبلغ المتبقي
                        decimal paidAmount = purchaseViewModel.Purchase.Receipts?.Sum(r => r.Value) ?? 0;
                        decimal remainingAmount = purchaseViewModel.Purchase.TotalAmount - paidAmount;

                        txtValue.Text = FormatDecimalValue(remainingAmount);
                        txtStatment.Text = $"دفع فاتورة مشتريات رقم {invoiceNo}";

                        if (remainingAmount <= 0)
                        {
                            ErrorBox.Show("هذه الفاتورة مدفوعة بالكامل", "تنبيه", false);
                        }
                    }
                    else
                    {
                        ErrorBox.Show("لم يتم العثور على فاتورة مشتريات بهذا الرقم", "تنبيه", false);
                        ResetInvoiceFields();
                    }
                }
                else if (financialId == (byte)FinancialId.Sale)
                {
                    // البحث في المبيعات
                    var saleViewModel = await _saleService.GetSaleByInvoiceNoAsync(invoiceNo);
                    if (saleViewModel != null)
                    {
                        _loadedSale = saleViewModel.Sale;
                        // تعبئة بيانات العميل والمبلغ
                        if (saleViewModel.Sale.ClientId > 0)
                        {
                            cmbClient.SelectedValue = saleViewModel.Sale.ClientId;
                            cmbClient.IsEnabled = false; // تعطيل قائمة العملاء عند تحديد فاتورة
                        }

                        // حساب المبلغ المتبقي
                        decimal paidAmount = saleViewModel.Sale.Receipts?.Sum(r => r.Value) ?? 0;
                        decimal remainingAmount = saleViewModel.Sale.TotalAmount - paidAmount;

                        txtValue.Text = FormatDecimalValue(remainingAmount);
                        txtStatment.Text = $"تحصيل فاتورة مبيعات رقم {invoiceNo}";

                        if (remainingAmount <= 0)
                        {
                            ErrorBox.Show("هذه الفاتورة محصلة بالكامل", "تنبيه", false);
                        }
                    }
                    else
                    {
                        ErrorBox.Show("لم يتم العثور على فاتورة مبيعات بهذا الرقم", "تنبيه", false);
                        ResetInvoiceFields();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء البحث عن الفاتورة: {ex.Message}", "خطأ", true);
                ResetInvoiceFields();
            }
        }

        private void ResetInvoiceFields()
        {
            _loadedPurchase = null;
            _loadedSale = null;
            cmbClient.IsEnabled = true;
            txtInvoiceNo.Text = string.Empty;
            txtValue.Text = "0";
            txtStatment.Text = string.Empty;
        }

        /// <summary>
        /// إنشاء إيصالات متعددة للعميل المختار (توزيع المبلغ على الفواتير المتبقية)
        /// </summary>
        private async Task CreateMultipleReceiptsForClient()
        {
            try
            {
                if (!TryParseDecimalValue(txtValue.Text, out decimal totalAmount) || totalAmount <= 0)
                {
                    ErrorBox.Show("يجب إدخال مبلغ صحيح أكبر من صفر (يمكن استخدام الفاصلة العشرية)", "بيانات خاطئة", false);
                    txtValue.Focus();
                    txtValue.SelectAll();
                    return;
                }

                int clientId = (int)cmbClient.SelectedValue;
                byte financialId = (byte)cmbType.SelectedValue;
                bool isSale = financialId == (byte)FinancialId.Sale;

                // التحقق من وجود فواتير متبقية أولاً (استعلام سريع)
                bool hasPendingInvoices = isSale
                    ? await _saleService.HasPendingSalesForClientAsync(clientId)
                    : await _purchaseService.HasPendingPurchasesForClientAsync(clientId);

                if (!hasPendingInvoices)
                {
                    ErrorBox.Show($"لا توجد فواتير {(isSale ? "مبيعات" : "مشتريات")} متبقية لهذا العميل", "تنبيه", false);
                    return;
                }

                // حساب إجمالي المبلغ المتبقي (استعلام سريع)
                decimal totalRemaining = isSale
                    ? await _saleService.GetTotalRemainingSalesAmountForClientAsync(clientId)
                    : await _purchaseService.GetTotalRemainingPurchasesAmountForClientAsync(clientId);

                if (totalAmount > totalRemaining)
                {
                    ErrorBox.Show($"المبلغ المدخل ({totalAmount:N2}) يتجاوز إجمالي المبلغ المتبقي ({totalRemaining:N2})", "مبلغ خاطئ", false);
                    return;
                }

                // جلب الفواتير المتبقية للعميل (فقط عند الحاجة)
                var pendingInvoices = await GetPendingInvoicesForClient(clientId, isSale);

                // تأكيد إنشاء إيصالات متعددة
                var confirmResult = QuestionBox.Show("تأكيد العملية",
                    $"سيتم إنشاء {pendingInvoices.Count(inv => totalAmount >= inv.RemainingAmount)} إيصال أو أكثر لتوزيع المبلغ {totalAmount:N2} على الفواتير المتبقية.\nهل تريد المتابعة؟");

                if (confirmResult != MessageBoxResult.Yes)
                    return;

                // إنشاء الإيصالات
                var createdReceipts = await CreateReceiptsForInvoices(pendingInvoices, totalAmount, isSale);

                if (createdReceipts.Any())
                {
                    ShowMultipleReceiptsCreatedMessage(createdReceipts);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء إنشاء الإيصالات: {ex.Message}", "خطأ", true);
            }
        }

        /// <summary>
        /// إنشاء إيصال واحد للفاتورة المحددة
        /// </summary>
        private async Task CreateSingleReceiptForInvoice()
        {
            try
            {
                if (!TryParseDecimalValue(txtValue.Text, out decimal amount) || amount <= 0)
                {
                    ErrorBox.Show("يجب إدخال مبلغ صحيح أكبر من صفر (يمكن استخدام الفاصلة العشرية)", "بيانات خاطئة", false);
                    txtValue.Focus();
                    txtValue.SelectAll();
                    return;
                }

                byte financialId = (byte)cmbType.SelectedValue;

                // التحقق من وجود فاتورة محملة
                if (financialId == (byte)FinancialId.Purchase && _loadedPurchase == null)
                {
                    ErrorBox.Show("يجب تحميل فاتورة المشتريات أولاً", "بيانات ناقصة", false);
                    return;
                }

                if (financialId == (byte)FinancialId.Sale && _loadedSale == null)
                {
                    ErrorBox.Show("يجب تحميل فاتورة المبيعات أولاً", "بيانات ناقصة", false);
                    return;
                }

                // إنشاء الإيصال
                var receipt = new Receipt
                {
                    Id = _isEditMode ? _receiptId : 0,
                    Value = amount,
                    Statement = txtStatment.Text,
                    Date = DtpGeneralExpireOn.SelectedDate ?? DateTime.Today,
                    FinancialId = financialId,
                    WarehouseId = cmbWarehouse.SelectedValue != null ? (int)cmbWarehouse.SelectedValue : (int?)null,
                    IsExchange = financialId == (byte)FinancialId.Purchase, // Purchase = صرف، Sale = قبض
                    ClientId = (int)cmbClient.SelectedValue,
                    TreasuryId = cmbTreasury.SelectedValue != null ? (byte?)cmbTreasury.SelectedValue : (byte?)null
                };

                // ربط الفاتورة
                if (financialId == (byte)FinancialId.Purchase)
                {
                    receipt.PurchaseId = _loadedPurchase.Id;
                }
                else if (financialId == (byte)FinancialId.Sale)
                {
                    receipt.SaleId = _loadedSale.Id;
                }

                // حفظ الإيصال
                if (_isEditMode)
                {
                    var (success, message, receiptId) = await _receiptService.UpdateReceipt(receipt);
                    if (success)
                    {
                        DialogBox.Show("تم بنجاح", message);
                        await LoadReceiptDataAfterSave(receiptId);
                        btnPrint.IsEnabled = true;
                        btnPrint.Opacity = 1.0;
                    }
                    else
                    {
                        ErrorBox.Show(message, "خطأ في الحفظ", false);
                    }
                }
                else
                {
                    var (success, message, receiptId) = await _receiptService.CreateReceipt(receipt);
                    if (success)
                    {
                        DialogBox.Show("تم بنجاح", message);
                        _receiptId = receiptId;
                        _isEditMode = true;
                        this.Title = "تعديل الإيصال";
                        await LoadReceiptDataAfterSave(receiptId);
                        btnPrint.IsEnabled = true;
                        btnPrint.Opacity = 1.0;
                    }
                    else
                    {
                        ErrorBox.Show(message, "خطأ في الحفظ", false);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حفظ الإيصال: {ex.Message}", "خطأ في الحفظ", true);
            }
        }

        /// <summary>
        /// جلب الفواتير المتبقية للعميل (التي بها بواقي فقط)
        /// </summary>
        private async Task<List<PendingInvoiceViewModel>> GetPendingInvoicesForClient(int clientId, bool isSale)
        {
            try
            {
                if (isSale)
                {
                    // جلب فواتير المبيعات المتبقية فقط
                    return await _saleService.GetPendingSalesByClientIdAsync(clientId);
                }
                else
                {
                    // جلب فواتير المشتريات المتبقية فقط
                    return await _purchaseService.GetPendingPurchasesByClientIdAsync(clientId);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء جلب الفواتير المتبقية: {ex.Message}", "خطأ", true);
                return new List<PendingInvoiceViewModel>();
            }
        }

        /// <summary>
        /// إنشاء إيصالات للفواتير المحددة
        /// </summary>
        private async Task<List<Receipt>> CreateReceiptsForInvoices(List<PendingInvoiceViewModel> pendingInvoices, decimal totalAmount, bool isSale)
        {
            var createdReceipts = new List<Receipt>();
            decimal remainingAmount = totalAmount;

            try
            {
                foreach (var invoice in pendingInvoices)
                {
                    if (remainingAmount <= 0) break;

                    decimal amountForThisInvoice = Math.Min(remainingAmount, invoice.RemainingAmount);

                    var receipt = new Receipt
                    {
                        Value = amountForThisInvoice,
                        Statement = $"{(isSale ? "تحصيل" : "دفع")} فاتورة {(isSale ? "مبيعات" : "مشتريات")} رقم {invoice.InvoiceNo}",
                        Date = DtpGeneralExpireOn.SelectedDate ?? DateTime.Today,
                        FinancialId = (byte)(isSale ? FinancialId.Sale : FinancialId.Purchase),
                        WarehouseId = cmbWarehouse.SelectedValue != null ? (int)cmbWarehouse.SelectedValue : (int?)null,
                        IsExchange = !isSale, // Purchase = صرف، Sale = قبض
                        ClientId = (int)cmbClient.SelectedValue,
                        TreasuryId = cmbTreasury.SelectedValue != null ? (byte?)cmbTreasury.SelectedValue : (byte?)null
                    };

                    // ربط الفاتورة
                    if (isSale)
                    {
                        receipt.SaleId = invoice.InvoiceId;
                    }
                    else
                    {
                        receipt.PurchaseId = invoice.InvoiceId;
                    }

                    // إضافة الإيصال إلى القائمة
                    createdReceipts.Add(receipt);
                    remainingAmount -= amountForThisInvoice;
                }

                // حفظ الإيصالات واحداً تلو الآخر لضمان ترقيم منفصل
                if (createdReceipts.Any())
                {
                    var savedReceipts = new List<Receipt>();

                    foreach (var receipt in createdReceipts)
                    {
                        var (success, message, receiptId) = await _receiptService.CreateReceipt(receipt);
                        if (success)
                        {
                            receipt.Id = receiptId;
                            savedReceipts.Add(receipt);
                        }
                        else
                        {
                            ErrorBox.Show($"فشل في حفظ إيصال للفاتورة: {message}", "خطأ", false);
                            break;
                        }
                    }

                    createdReceipts = savedReceipts;
                }

                return createdReceipts;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء إنشاء الإيصالات: {ex.Message}", "خطأ", true);
                return createdReceipts;
            }
        }

        /// <summary>
        /// عرض رسالة نجاح إنشاء إيصالات متعددة
        /// </summary>
        private void ShowMultipleReceiptsCreatedMessage(List<Receipt> createdReceipts)
        {
            try
            {
                string message = $"تم إنشاء {createdReceipts.Count} إيصال بنجاح:\n\n";

                foreach (var receipt in createdReceipts)
                {
                    message += $"• إيصال رقم: {receipt.ReceiptNumber}\n";
                    message += $"  المبلغ: {receipt.Value:N2}\n";
                    message += $"  البيان: {receipt.Statement}\n\n";
                }

                message += $"إجمالي المبلغ: {createdReceipts.Sum(r => r.Value):N2}";

                DialogBox.Show("تم إنشاء الإيصالات بنجاح", message);
            }
            catch (Exception ex)
            {
                DialogBox.Show("تم إنشاء الإيصالات بنجاح", $"تم إنشاء {createdReceipts.Count} إيصال بإجمالي مبلغ {createdReceipts.Sum(r => r.Value):N2}");
            }
        }


        // أحداث الأزرار
        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                await SaveReceipt();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnPrint_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_isEditMode && _receiptId > 0)
            {
                // طباعة الإيصال الحالي
                await PrintReceipt(_receiptId);
            }
            else
            {
                ErrorBox.Show("يجب حفظ الإيصال أولاً قبل الطباعة", "تنبيه", false);
            }
        }



        private void btnCancel_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            this.Close();
        }

        private async Task SaveReceipt()
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateData())
                {
                    return;
                }

                byte? financialId = (byte?)cmbType.SelectedValue;

                // التحقق من السيناريوهات الخاصة لفواتير المبيعات والمشتريات
                if (financialId == (byte)FinancialId.Sale || financialId == (byte)FinancialId.Purchase)
                {
                    // في وضع التعديل، يجب دائماً استخدام الخيار الثاني (رقم فاتورة محددة)
                    if (_isEditMode)
                    {
                        if (string.IsNullOrEmpty(txtInvoiceNo.Text))
                        {
                            ErrorBox.Show("في وضع التعديل، يجب إدخال رقم فاتورة محددة", "بيانات ناقصة", false);
                            return;
                        }
                        await CreateSingleReceiptForInvoice();
                        return;
                    }

                    // في وضع الإضافة، يمكن استخدام كلا الخيارين
                    // إذا لم يتم إدخال رقم فاتورة ولكن تم اختيار عميل - إنشاء إيصالات متعددة
                    if (string.IsNullOrEmpty(txtInvoiceNo.Text) && cmbClient.SelectedValue != null)
                    {
                        await CreateMultipleReceiptsForClient();
                        return;
                    }
                    // إذا تم إدخال رقم فاتورة - إنشاء إيصال واحد للفاتورة المحددة
                    else if (!string.IsNullOrEmpty(txtInvoiceNo.Text))
                    {
                        await CreateSingleReceiptForInvoice();
                        return;
                    }
                    else
                    {
                        ErrorBox.Show("يجب إما اختيار عميل أو إدخال رقم فاتورة", "بيانات ناقصة", false);
                        return;
                    }
                }

                // تحديد قيم الحقول حسب نوع الإيصال (للأنواع الأخرى)
                int? clientId = null;
                int? employeeId = null;
                int? expenseId = null;
                byte? treasuryId = null;
                byte? sourceTreasuryId = null;
                byte? targetTreasuryId = null;
                bool? isExchange = null;

                if (financialId.HasValue)
                {
                    switch (financialId.Value)
                    {
                        case (byte)FinancialId.Client:
                        case (byte)FinancialId.Sale:
                        case (byte)FinancialId.Purchase:
                        case (byte)FinancialId.OpeningBalanceForClient:
                            clientId = cmbClient.SelectedValue != null ? (int)cmbClient.SelectedValue : (int?)null;
                            treasuryId = cmbTreasury.SelectedValue != null ? (byte?)cmbTreasury.SelectedValue : (byte?)null;
                            isExchange = RdbCatch.IsChecked == true;
                            break;

                        case (byte)FinancialId.Employee:
                        case (byte)FinancialId.OpeningBalanceForEmployee:
                        case (byte)FinancialId.SalaryPayment:
                            employeeId = cmbEmploye.SelectedValue != null ? (int)cmbEmploye.SelectedValue : (int?)null;
                            treasuryId = cmbTreasury.SelectedValue != null ? (byte?)cmbTreasury.SelectedValue : (byte?)null;
                            isExchange = RdbCatch.IsChecked == true;
                            break;

                        case (byte)FinancialId.Expense:
                            expenseId = cmbExpense.SelectedValue != null ? (int)cmbExpense.SelectedValue : (int?)null;
                            treasuryId = cmbTreasury.SelectedValue != null ? (byte?)cmbTreasury.SelectedValue : (byte?)null;
                            isExchange = true; // المصروفات دائماً صرف
                            break;

                        case (byte)FinancialId.Deposit:
                            treasuryId = cmbTreasury.SelectedValue != null ? (byte?)cmbTreasury.SelectedValue : (byte?)null;
                            isExchange = false; // الإيداع دائماً قبض
                            break;

                        case (byte)FinancialId.Withdrawal:
                            treasuryId = cmbTreasury.SelectedValue != null ? (byte?)cmbTreasury.SelectedValue : (byte?)null;
                            isExchange = true; // السحب دائماً صرف
                            break;

                        case (byte)FinancialId.Transfer:
                            sourceTreasuryId = cmbSourceTreasury.SelectedValue != null ? (byte?)cmbSourceTreasury.SelectedValue : (byte?)null;
                            targetTreasuryId = cmbTargetTreasury.SelectedValue != null ? (byte?)cmbTargetTreasury.SelectedValue : (byte?)null;
                            isExchange = null; // التحويل ليس إيراد ولا مصروف
                            break;
                    }
                }

                // تحويل القيمة (تم التحقق من صحتها مسبقاً في ValidateInput)
                TryParseDecimalValue(txtValue.Text, out decimal receiptValue);

                // إعداد بيانات الإيصال
                var receipt = new Receipt
                {
                    Id = _isEditMode ? _receiptId : 0,
                    ReceiptNumber = txtReceiptNo.Text,
                    Value = receiptValue,
                    Statement = txtStatment.Text,
                    Date = DtpGeneralExpireOn.SelectedDate ?? DateTime.Today,
                    FinancialId = financialId,
                    WarehouseId = cmbWarehouse.SelectedValue != null ? (int)cmbWarehouse.SelectedValue : (int?)null,
                    IsExchange = isExchange,
                    ClientId = clientId,
                    EmployeeId = employeeId,
                    ExpenseId = expenseId,
                    TreasuryId = treasuryId,
                    SourceTreasuryId = sourceTreasuryId,
                    TargetTreasuryId = targetTreasuryId
                };

                // ربط الفاتورة إذا وجدت
                if (_loadedPurchase != null)
                {
                    receipt.PurchaseId = _loadedPurchase.Id;
                }
                else if (_loadedSale != null)
                {
                    receipt.SaleId = _loadedSale.Id;
                }

                // حفظ الإيصال
                if (_isEditMode)
                {
                    var (success, message, receiptId) = await _receiptService.UpdateReceipt(receipt);
                    if (success)
                    {
                        DialogBox.Show("تم بنجاح", message);

                        // جلب بيانات الإيصال المحدثة وعرضها
                        await LoadReceiptDataAfterSave(receiptId);

                        // تفعيل زر الطباعة
                        btnPrint.IsEnabled = true;
                        btnPrint.Opacity = 1.0;
                    }
                    else
                    {
                        ErrorBox.Show(message, "خطأ في الحفظ", false);
                    }
                }
                else
                {
                    var (success, message, receiptId) = await _receiptService.CreateReceipt(receipt);
                    if (success)
                    {
                        DialogBox.Show("تم بنجاح", message);

                        // في حالة الإضافة، استخدام معرف الإيصال المرجع
                        _receiptId = receiptId;
                        _isEditMode = true;
                        this.Title = "تعديل الإيصال";

                        // جلب بيانات الإيصال المحدثة وعرضها
                        await LoadReceiptDataAfterSave(receiptId);

                        // تفعيل زر الطباعة
                        btnPrint.IsEnabled = true;
                        btnPrint.Opacity = 1.0;
                    }
                    else
                    {
                        ErrorBox.Show(message, "خطأ في الحفظ", false);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حفظ الإيصال: {ex.Message}", "خطأ في الحفظ", true);
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateData()
        {
            try
            {
                // التحقق من نوع الإيصال
                if (cmbType.SelectedValue == null)
                {
                    ErrorBox.Show("الرجاء اختيار نوع الإيصال", "بيانات ناقصة", false);
                    cmbType.Focus();
                    return false;
                }

                // التحقق من المبلغ
                if (string.IsNullOrWhiteSpace(txtValue.Text))
                {
                    ErrorBox.Show("الرجاء إدخال المبلغ", "بيانات ناقصة", false);
                    txtValue.Focus();
                    return false;
                }

                if (!TryParseDecimalValue(txtValue.Text, out decimal value) || value <= 0)
                {
                    ErrorBox.Show("الرجاء إدخال مبلغ صحيح أكبر من صفر (يمكن استخدام الفاصلة العشرية)", "بيانات خاطئة", false);
                    txtValue.Focus();
                    txtValue.SelectAll();
                    return false;
                }

                // التحقق من المخزن
                if (cmbWarehouse.SelectedValue == null)
                {
                    ErrorBox.Show("الرجاء اختيار المخزن", "بيانات ناقصة", false);
                    cmbWarehouse.Focus();
                    return false;
                }

                byte financialId = (byte)cmbType.SelectedValue;

                // التحقق من الحقول المطلوبة حسب نوع الإيصال
                switch (financialId)
                {
                    case (byte)FinancialId.Client:
                    case (byte)FinancialId.OpeningBalanceForClient:
                        if (cmbClient.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار العميل", "بيانات ناقصة", false);
                            cmbClient.Focus();
                            return false;
                        }

                        // للأرصدة الافتتاحية لا نحتاج طريقة دفع
                        if (financialId != (byte)FinancialId.OpeningBalanceForClient)
                        {
                            if (cmbTreasury.SelectedValue == null)
                            {
                                ErrorBox.Show("الرجاء اختيار طريقة الدفع", "بيانات ناقصة", false);
                                cmbTreasury.Focus();
                                return false;
                            }
                        }
                        break;

                    case (byte)FinancialId.Sale:
                    case (byte)FinancialId.Purchase:
                        // للفواتير، التحقق من وجود عميل أو رقم فاتورة
                        if (cmbClient.SelectedValue == null && string.IsNullOrWhiteSpace(txtInvoiceNo.Text))
                        {
                            ErrorBox.Show("يجب إما اختيار عميل أو إدخال رقم فاتورة", "بيانات ناقصة", false);
                            cmbClient.Focus();
                            return false;
                        }

                        // إذا تم إدخال رقم فاتورة، التحقق من أنه لا يوجد عميل مختار
                        if (!string.IsNullOrWhiteSpace(txtInvoiceNo.Text) && cmbClient.SelectedValue != null && cmbClient.IsEnabled)
                        {
                            ErrorBox.Show("لا يمكن اختيار عميل وإدخال رقم فاتورة في نفس الوقت", "بيانات خاطئة", false);
                            return false;
                        }

                        // التحقق من طريقة الدفع
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار طريقة الدفع", "بيانات ناقصة", false);
                            cmbTreasury.Focus();
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Employee:
                    case (byte)FinancialId.SalaryPayment:
                        if (cmbEmploye.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الموظف", "بيانات ناقصة", false);
                            cmbEmploye.Focus();
                            return false;
                        }
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار طريقة الدفع", "بيانات ناقصة", false);
                            cmbTreasury.Focus();
                            return false;
                        }
                        break;

                    case (byte)FinancialId.OpeningBalanceForEmployee:
                        if (cmbEmploye.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الموظف", "بيانات ناقصة", false);
                            cmbEmploye.Focus();
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Expense:
                        if (cmbExpense.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار المصروف", "بيانات ناقصة", false);
                            cmbExpense.Focus();
                            return false;
                        }
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار طريقة الدفع", "بيانات ناقصة", false);
                            cmbTreasury.Focus();
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Deposit:
                    case (byte)FinancialId.Withdrawal:
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار طريقة الدفع", "بيانات ناقصة", false);
                            cmbTreasury.Focus();
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Transfer:
                        if (cmbSourceTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار طريقة الدفع المصدر", "بيانات ناقصة", false);
                            cmbSourceTreasury.Focus();
                            return false;
                        }
                        if (cmbTargetTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار طريقة الدفع الهدف", "بيانات ناقصة", false);
                            cmbTargetTreasury.Focus();
                            return false;
                        }
                        if (cmbSourceTreasury.SelectedValue.Equals(cmbTargetTreasury.SelectedValue))
                        {
                            ErrorBox.Show("لا يمكن التحويل من وإلى نفس طريقة الدفع", "بيانات خاطئة", false);
                            cmbTargetTreasury.Focus();
                            return false;
                        }
                        break;

                    default:
                        ErrorBox.Show("نوع الإيصال المختار غير مدعوم", "خطأ في النوع", false);
                        cmbType.Focus();
                        return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء التحقق من البيانات: {ex.Message}", "خطأ في التحقق", true);
                return false;
            }
        }

        private async Task PrintReceipt(int receiptId)
        {
            try
            {
                // التأكد من وجود بيانات الإيصال
                if (_currentReceipt == null || _currentReceipt.Id != receiptId)
                {
                    // جلب بيانات الإيصال إذا لم تكن محملة
                    _currentReceipt = await _receiptService.GetReceiptById(receiptId);
                }

                if (_currentReceipt == null || _currentReceipt.Id == 0)
                {
                    ErrorBox.Show("يرجى اختيار ايصال للطباعة", "خطأ");
                    return;
                }

                // استخدام نفس نظام الطباعة المستخدم في ReceiptPage
                SinglePagePdfPrinter pdfPrinter = new SinglePagePdfPrinter();
                PrintableReciept reciept = new PrintableReciept(_currentReceipt);
                string fileName = $"reciept_{DateTime.Now:yyyyMMdd_HHmmss}";
                var printerNames = GetDefaultPrinterName();

                pdfPrinter.ExportAndPrint(reciept, @"C:\PDFs", "reciept", printerNames);
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء طباعة الإيصال: {ex.Message}", "خطأ في الطباعة", true);
            }
        }





        private async Task ConfigureUIByType()
        {
            if (cmbType.SelectedItem is Financial selectedFinancial)
            {
                // إعادة تعيين عناصر التحكم إلى حالتها الافتراضية
                cmbClient.IsEnabled = true;
                cmbEmploye.IsEnabled = true;
                cmbExpense.IsEnabled = true;
                RdbCatch.IsEnabled = true;
                RdbExchange.IsEnabled = true;
                txtInvoiceNo.IsEnabled = true;
                cmbTreasury.IsEnabled = true;
                cmbTreasury.Visibility = Visibility.Visible;
                cmbExpense.Visibility = Visibility.Collapsed;

                // إخفاء عناصر التحويل بشكل افتراضي
                cmbSourceTreasury.Visibility = Visibility.Collapsed;
                cmbTargetTreasury.Visibility = Visibility.Collapsed;

                // تكوين واجهة المستخدم حسب نوع العملية المالية
                switch (selectedFinancial.Id)
                {
                    case (byte)FinancialId.Client: // عميل
                        cmbClient.Visibility = Visibility.Visible;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // البيانات ستحمل تلقائياً عند اختيار النوع
                        break;

                    case (byte)FinancialId.Deposit: // إيداع
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // دائماً يكون قبض (إيراد) ولا يمكن تغييره
                        RdbExchange.IsChecked = true; // قبض
                        RdbCatch.IsChecked = false;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        break;

                    case (byte)FinancialId.Withdrawal: // سحب
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // دائماً يكون صرف (مصروف) ولا يمكن تغييره
                        RdbCatch.IsChecked = true; // صرف
                        RdbExchange.IsChecked = false;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        break;

                    case (byte)FinancialId.Employee: // موظف
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Visible;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل الموظفين عند الحاجة
                        await LoadEmployees();
                        break;

                    case (byte)FinancialId.Sale: // فاتورة مبيعات
                        cmbClient.Visibility = Visibility.Visible;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Visible;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // دائماً يكون إيراد (قبض) ولا يمكن تغييره
                        RdbExchange.IsChecked = true;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;

                        // في وضع التعديل، يجب استخدام الخيار الثاني فقط (رقم فاتورة محددة)
                        if (_isEditMode)
                        {
                            cmbClient.IsEnabled = false;
                            txtInvoiceNo.IsEnabled = true;
                            cmbClient.ToolTip = "في وضع التعديل، لا يمكن تغيير العميل";
                            txtInvoiceNo.ToolTip = "في وضع التعديل، يجب استخدام رقم فاتورة محددة";
                        }
                        else
                        {
                            // في وضع الإضافة، تمكين كلا الخيارين
                            cmbClient.IsEnabled = true;
                            txtInvoiceNo.IsEnabled = true;
                            cmbClient.ToolTip = null;
                            txtInvoiceNo.ToolTip = null;
                        }
                        break;

                    case (byte)FinancialId.Purchase: // فاتورة مشتريات
                        cmbClient.Visibility = Visibility.Visible;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Visible;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // دائماً يكون مصروف (صرف) ولا يمكن تغييره
                        RdbCatch.IsChecked = true;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;

                        // في وضع التعديل، يجب استخدام الخيار الثاني فقط (رقم فاتورة محددة)
                        if (_isEditMode)
                        {
                            cmbClient.IsEnabled = false;
                            txtInvoiceNo.IsEnabled = true;
                            cmbClient.ToolTip = "في وضع التعديل، لا يمكن تغيير العميل";
                            txtInvoiceNo.ToolTip = "في وضع التعديل، يجب استخدام رقم فاتورة محددة";
                        }
                        else
                        {
                            // في وضع الإضافة، تمكين كلا الخيارين
                            cmbClient.IsEnabled = true;
                            txtInvoiceNo.IsEnabled = true;
                            cmbClient.ToolTip = null;
                            txtInvoiceNo.ToolTip = null;
                        }
                        break;

                    case (byte)FinancialId.OpeningBalanceForClient: // رصيد افتتاحي للعميل
                        cmbClient.Visibility = Visibility.Visible;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Collapsed;
                        cmbTreasury.SelectedValue = null;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // البيانات ستحمل تلقائياً عند اختيار النوع
                        break;

                    case (byte)FinancialId.OpeningBalanceForEmployee: // رصيد افتتاحي للموظف
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Visible;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Collapsed;
                        cmbTreasury.SelectedValue = null;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل الموظفين عند الحاجة
                        await LoadEmployees();
                        break;

                    case (byte)FinancialId.Expense: // مصروفات
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        cmbExpense.Visibility = Visibility.Visible;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل المصروفات عند الحاجة
                        await LoadExpenses();
                        // دائماً يكون مصروف (صرف) ولا يمكن تغييره
                        RdbCatch.IsChecked = true;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        break;

                    case (byte)FinancialId.SalaryPayment: // صرف راتب
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Visible;
                        cmbExpense.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل الموظفين عند الحاجة
                        await LoadEmployees();
                        // دائماً يكون مصروف (صرف) ولا يمكن تغييره
                        RdbCatch.IsChecked = true;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        break;

                    case (byte)FinancialId.Transfer: // تحويل بين طرق الدفع
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        cmbExpense.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Collapsed;
                        cmbSourceTreasury.Visibility = Visibility.Visible;
                        cmbTargetTreasury.Visibility = Visibility.Visible;

                        // تحميل جميع طرق الدفع من جميع المخازن
                        await LoadAllTreasuriesForTransfer();

                        // ليس إيراد ولا مصروف - تعطيل كليهما
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        RdbCatch.IsChecked = null;
                        RdbExchange.IsChecked = null;
                        break;
                }
            }
        }



        /// <summary>
        /// تنظيف وتحويل النص إلى قيمة عشرية
        /// </summary>
        /// <param name="text">النص المراد تحويله</param>
        /// <param name="value">القيمة العشرية الناتجة</param>
        /// <returns>true إذا تم التحويل بنجاح، false إذا فشل</returns>
        private bool TryParseDecimalValue(string text, out decimal value)
        {
            value = 0;
            if (string.IsNullOrWhiteSpace(text))
                return false;

            // تنظيف النص: استبدال الفاصلة بالنقطة
            string cleanText = text.Replace(",", ".");

            // محاولة التحويل باستخدام الثقافة المحايدة
            return decimal.TryParse(cleanText,
                System.Globalization.NumberStyles.AllowDecimalPoint,
                System.Globalization.CultureInfo.InvariantCulture,
                out value);
        }

        /// <summary>
        /// تنسيق القيمة العشرية للعرض
        /// </summary>
        /// <param name="value">القيمة العشرية</param>
        /// <returns>النص المنسق</returns>
        private string FormatDecimalValue(decimal value)
        {
            return value.ToString("N2", System.Globalization.CultureInfo.CurrentCulture);
        }



        private async Task LoadReceiptDataAfterSave(int receiptId)
        {
            try
            {
                // جلب بيانات الإيصال المحدثة من قاعدة البيانات
                var savedReceipt = await _receiptService.GetReceiptById(receiptId);
                if (savedReceipt != null)
                {
                    // تحديث رقم الإيصال المولد تلقائياً
                    txtReceiptNo.Text = savedReceipt.ReceiptNumber;

                    // تحديث البيانات الأخرى إذا تم تعديلها من قبل النظام
                    if (!string.IsNullOrEmpty(savedReceipt.Statement) && savedReceipt.Statement != txtStatment.Text)
                    {
                        txtStatment.Text = savedReceipt.Statement;
                    }

                    // حفظ الإيصال المحدث في المتغير المحلي
                    _currentReceipt = savedReceipt;

                    // عرض رسالة تأكيد مع رقم الإيصال
                    ShowReceiptSavedMessage(savedReceipt);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء جلب بيانات الإيصال: {ex.Message}", "خطأ في جلب البيانات", true);
            }
        }

        private void ShowReceiptSavedMessage(Receipt receipt)
        {
            try
            {
                string receiptType = "";
                if (cmbType.SelectedItem is Financial financial)
                {
                    receiptType = financial.Name;
                }

                string clientOrEmployee = "";
                if (receipt.ClientId.HasValue && cmbClient.SelectedItem is Client client)
                {
                    clientOrEmployee = $"للعميل: {client.Name}";
                }
                else if (receipt.EmployeeId.HasValue && cmbEmploye.SelectedItem is User employee)
                {
                    clientOrEmployee = $"للموظف: {employee.Name}";
                }
                else if (receipt.ExpenseId.HasValue && cmbExpense.SelectedItem is Expense expense)
                {
                    clientOrEmployee = $"للمصروف: {expense.Name}";
                }

                string direction = "";
                if (receipt.IsExchange.HasValue)
                {
                    direction = receipt.IsExchange.Value ? "صرف" : "قبض";
                }

                string amount = receipt.Value.ToString("N2");

                string message = $"تم حفظ الإيصال بنجاح\n\n" +
                               $"رقم الإيصال: {receipt.ReceiptNumber}\n" +
                               $"النوع: {receiptType}\n" +
                               $"المبلغ: {amount}\n";

                if (!string.IsNullOrEmpty(direction))
                {
                    message += $"الاتجاه: {direction}\n";
                }

                if (!string.IsNullOrEmpty(clientOrEmployee))
                {
                    message += clientOrEmployee + "\n";
                }

                if (receipt.Date != null)
                {
                    message += $"التاريخ: {receipt.Date:yyyy-MM-dd}\n";
                }

                DialogBox.Show("تم الحفظ بنجاح", message);
            }
            catch (Exception ex)
            {
                // في حالة خطأ في عرض الرسالة، نعرض رسالة بسيطة
                DialogBox.Show("تم الحفظ بنجاح", $"تم حفظ الإيصال رقم: {receipt.ReceiptNumber}");
            }
        }

        private string GetDefaultPrinterName()
        {
            try
            {
                string defaultPrinter = Properties.Settings.Default.DefaultPrinter;
                return defaultPrinter;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الطابعة الافتراضية: {ex.Message}");
                return string.Empty; // Return empty if error occurs
            }
        }

        /// <summary>
        /// تنظيف الموارد عند إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // تنظيف المتغيرات
                _currentReceipt = null;
                _loadedPurchase = null;
                _loadedSale = null;

                // تنظيف القوائم
                _warehouses?.Clear();
                _financials?.Clear();
                _clients?.Clear();
                _treasuries?.Clear();
                _employees?.Clear();
                _expenses?.Clear();

                // إلغاء ربط البيانات
                if (cmbWarehouse != null) cmbWarehouse.ItemsSource = null;
                if (cmbType != null) cmbType.ItemsSource = null;
                if (cmbClient != null) cmbClient.ItemsSource = null;
                if (cmbTreasury != null) cmbTreasury.ItemsSource = null;
                if (cmbEmploye != null) cmbEmploye.ItemsSource = null;
                if (cmbExpense != null) cmbExpense.ItemsSource = null;
                if (cmbSourceTreasury != null) cmbSourceTreasury.ItemsSource = null;
                if (cmbTargetTreasury != null) cmbTargetTreasury.ItemsSource = null;
            }
            catch (Exception ex)
            {
                // تجنب إظهار أخطاء أثناء الإغلاق
                System.Diagnostics.Debug.WriteLine($"Error during cleanup: {ex.Message}");
            }
            finally
            {
                base.OnClosed(e);
            }
        }

        /// <summary>
        /// إعادة تعيين النموذج للحالة الافتراضية
        /// </summary>
        private void ResetForm()
        {
            try
            {
                // إعادة تعيين الحقول النصية
                txtReceiptNo.Text = string.Empty;
                txtValue.Text = "0";
                txtStatment.Text = string.Empty;
                txtInvoiceNo.Text = string.Empty;

                // إعادة تعيين التاريخ
                DtpGeneralExpireOn.SelectedDate = DateTime.Today;

                // إعادة تعيين القوائم المنسدلة
                cmbType.SelectedValue = null;
                cmbClient.SelectedValue = null;
                cmbEmploye.SelectedValue = null;
                cmbExpense.SelectedValue = null;
                cmbTreasury.SelectedValue = null;
                cmbSourceTreasury.SelectedValue = null;
                cmbTargetTreasury.SelectedValue = null;

                // إعادة تعيين أزرار الراديو
                RdbExchange.IsChecked = true;
                RdbCatch.IsChecked = false;

                // إعادة تعيين المتغيرات
                _currentReceipt = null;
                _loadedPurchase = null;
                _loadedSale = null;
                _isEditMode = false;
                _receiptId = 0;

                // تعطيل زر الطباعة
                btnPrint.IsEnabled = false;
                btnPrint.Opacity = 0.5;

                // إخفاء جميع الحقول الاختيارية
                ConfigureUIByType();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء إعادة تعيين النموذج: {ex.Message}", "خطأ", true);
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                ResetForm();
            }
            finally
            {
                EnableAllButtons();
            }
        }
    }
}