using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.Converters;
using System.Windows.Media;

namespace VisionPoint.UI.views.Pages.Sales
{
    /// <summary>
    /// Interaction logic for AddSalesLenses.xaml
    /// </summary>
    public partial class AddSalesLenses : Window
    {
        private readonly LensService _lensService;
        private readonly DiscountManager _discountManager;
        private List<Lens> _lenses;
        private List<LensCategory> _lensCategories;
        private bool _isLoading = false;
        List<LensPrescription>? _prescriptions;
        private int _warehouseId;

        public AddSalesLenses(int warehouseId = 1)
        {
            InitializeComponent();
            _lensService = new LensService();
            _discountManager = new DiscountManager();
            _warehouseId = warehouseId;

            // Add event handlers for barcode text boxes
            txtBarCodRight.KeyDown += TxtBarCod_KeyDown;
            txtBarCodLeft.KeyDown += TxtBarCod_KeyDown;

            // إضافة معالجات أحداث للقوائم المنسدلة SPH, CYL, POW
            cmbSphRight.SelectionChanged += Cmb_SPH_CYL_POW_SelectionChanged;
            cmbCylRight.SelectionChanged += Cmb_SPH_CYL_POW_SelectionChanged;
            cmbpowRight.SelectionChanged += Cmb_SPH_CYL_POW_SelectionChanged;

            cmbSphLeft.SelectionChanged += Cmb_SPH_CYL_POW_SelectionChanged;
            cmbCylLeft.SelectionChanged += Cmb_SPH_CYL_POW_SelectionChanged;
            cmbpowLeft.SelectionChanged += Cmb_SPH_CYL_POW_SelectionChanged;
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                _isLoading = true;

                txtQuantityRight.Text="1";
                txtQuantityLeft.Text="1";
                txtAvailableQuantityLeft.Text="0";
                txtAvailableQuantityRight.Text="0";

                // تحميل العدسات المتاحة في المخزن المختار
                _lenses = await _lensService.GetLensesByWarehouseAsync(_warehouseId);

                // تحميل أنواع العدسات
                _lensCategories = await _lensService.GetAllLensCategoriesAsync();

                // إضافة خيار "كل الأنواع"
                var allCategoriesItem = new LensCategory { Id = 0, Name = "كل أنواع العدسات" };
                var categories = new List<LensCategory> { allCategoriesItem };
                categories.AddRange(_lensCategories);

                // تعبئة قوائم أنواع العدسات
                if (cmbLensCategoryRight != null)
                {
                    cmbLensCategoryRight.ItemsSource = categories;
                    cmbLensCategoryRight.DisplayMemberPath = "Name";
                    cmbLensCategoryRight.SelectedValuePath = "Id";
                    cmbLensCategoryRight.SelectedIndex = 0;
                }

                if (cmbLensCategoryLeft != null)
                {
                    cmbLensCategoryLeft.ItemsSource = categories;
                    cmbLensCategoryLeft.DisplayMemberPath = "Name";
                    cmbLensCategoryLeft.SelectedValuePath = "Id";
                    cmbLensCategoryLeft.SelectedIndex = 0;
                }

                // تعبئة قوائم العدسات
                if (cmbLensesNameRight != null)
                {
                    cmbLensesNameRight.ItemsSource = _lenses;
                    cmbLensesNameRight.DisplayMemberPath = "Name";
                    cmbLensesNameRight.SelectedValuePath = "Id";
                }

                if (cmbLensesNameLeft != null)
                {
                    cmbLensesNameLeft.ItemsSource = _lenses;
                    cmbLensesNameLeft.DisplayMemberPath = "Name";
                    cmbLensesNameLeft.SelectedValuePath = "Id";
                }

                // ربط أحداث تغيير الاختيار في cmbPrescriptionRight و cmbPrescriptionLeft
                if (cmbPrescriptionRight != null)
                {
                    cmbPrescriptionRight.SelectionChanged += cmbPrescriptionRight_SelectionChanged;
                }

                if (cmbPrescriptionLeft != null)
                {
                    cmbPrescriptionLeft.SelectionChanged += cmbPrescriptionLeft_SelectionChanged;
                }

                // تعيين الرؤية الأولية بناءً على حالة مربعات الاختيار
                UpdateRightLensVisibility(chkRghtLen?.IsChecked == true);
                UpdateLeftLensVisibility(chkLeftLen?.IsChecked == true);
            }
            finally
            {
                _isLoading = false;
            }
        }

        private void chkRghtLen_Checked(object sender, RoutedEventArgs e)
        {
            UpdateRightLensVisibility(true);
        }

        private void chkRghtLen_Unchecked(object sender, RoutedEventArgs e)
        {
            UpdateRightLensVisibility(false);
        }

        private void chkLeftLen_Checked(object sender, RoutedEventArgs e)
        {
            UpdateLeftLensVisibility(true);
        }

        private void chkLeftLen_Unchecked(object sender, RoutedEventArgs e)
        {
            UpdateLeftLensVisibility(false);
        }

        private void UpdateRightLensVisibility(bool isVisible)
        {
            // تحديث رؤية جميع حقول العين اليمنى
            var visibility = isVisible ? Visibility.Visible : Visibility.Collapsed;

            // تعيين رؤية حقول العين اليمنى
            if (txtBarCodRight != null) txtBarCodRight.Visibility = visibility;
            if (cmbLensCategoryRight != null) cmbLensCategoryRight.Visibility = visibility;
            if (cmbLensesNameRight != null) cmbLensesNameRight.Visibility = visibility;
            if (cmbColorRight != null) cmbColorRight.Visibility = visibility;
            if (cmbExpireOnRight != null) cmbExpireOnRight.Visibility = visibility;
            if (cmbPrescriptionRight != null) cmbPrescriptionRight.Visibility = visibility;
            if (txtAxisRight != null) txtAxisRight.Visibility = visibility;
            if (txtAddRight != null) txtAddRight.Visibility = visibility;
            if (txtBCRight != null) txtBCRight.Visibility = visibility;
            if (txtDiaRight != null) txtDiaRight.Visibility = visibility;
            if (txtPriceRight != null) txtPriceRight.Visibility = visibility;
            if (txtQuantityRight != null) txtQuantityRight.Visibility = visibility;
            if (txtAvailableQuantityRight != null) txtAvailableQuantityRight.Visibility = visibility;
            // إضافة التحكم في ظهور واختفاء عناصر SPH, CYL, POW
            if (cmbSphRight != null) cmbSphRight.Visibility = visibility;
            if (cmbCylRight != null) cmbCylRight.Visibility = visibility;
            if (cmbpowRight != null) cmbpowRight.Visibility = visibility;
            if (lblRightDiscount != null) lblRightDiscount.Visibility = Visibility.Collapsed;

            // تفريغ جميع الحقول عند إلغاء اختيار العدسة
            if (!isVisible)
            {
                _isLoading = true;
                try
                {
                    if (txtBarCodRight != null) txtBarCodRight.Text = "";
                    if (cmbLensCategoryRight != null) cmbLensCategoryRight.SelectedIndex = 0;
                    if (cmbLensesNameRight != null) cmbLensesNameRight.SelectedItem = null;
                    if (cmbColorRight != null) cmbColorRight.SelectedItem = null;
                    if (cmbExpireOnRight != null) cmbExpireOnRight.SelectedItem = null;
                    if (cmbPrescriptionRight != null) cmbPrescriptionRight.SelectedItem = null;
                    if (txtAxisRight != null)
                    {
                        txtAxisRight.Text = "";
                        txtAxisRight.Visibility = Visibility.Collapsed;
                        txtAxisRight.IsReadOnly = true;
                    }
                    if (txtAddRight != null) txtAddRight.Text = "";
                    if (txtBCRight != null) txtBCRight.Text = "";
                    if (txtDiaRight != null) txtDiaRight.Text = "";
                    if (txtPriceRight != null) txtPriceRight.Text = "";
                    if (txtQuantityRight != null) txtQuantityRight.Text = "1";
                    if (cmbSphRight != null) cmbSphRight.SelectedItem = null;
                    if (cmbCylRight != null) cmbCylRight.SelectedItem = null;
                    if (cmbpowRight != null) cmbpowRight.SelectedItem = null;
                    if (lblRightDiscount != null) lblRightDiscount.Content = "";
                    if (txtAvailableQuantityRight != null) txtAvailableQuantityRight.Text = "0";
                }
                finally
                {
                    _isLoading = false;
                }
            }
        }

        private void UpdateLeftLensVisibility(bool isVisible)
        {
            // تحديث رؤية جميع حقول العين اليسرى
            var visibility = isVisible ? Visibility.Visible : Visibility.Collapsed;

            // تعيين رؤية حقول العين اليسرى
            if (txtBarCodLeft != null) txtBarCodLeft.Visibility = visibility;
            if (cmbLensCategoryLeft != null) cmbLensCategoryLeft.Visibility = visibility;
            if (cmbLensesNameLeft != null) cmbLensesNameLeft.Visibility = visibility;
            if (cmbColorLeft != null) cmbColorLeft.Visibility = visibility;
            if (cmbExpireOnLeft != null) cmbExpireOnLeft.Visibility = visibility;
            if (cmbPrescriptionLeft != null) cmbPrescriptionLeft.Visibility = visibility;
            if (txtAxisLeft != null) txtAxisLeft.Visibility = visibility;
            if (txtAddLeft != null) txtAddLeft.Visibility = visibility;
            if (txtBCLeft != null) txtBCLeft.Visibility = visibility;
            if (txtDiaLeft != null) txtDiaLeft.Visibility = visibility;
            if (txtPriceLeft != null) txtPriceLeft.Visibility = visibility;
            if (txtQuantityLeft != null) txtQuantityLeft.Visibility = visibility;
            if(txtAvailableQuantityLeft != null) txtAvailableQuantityLeft.Visibility = visibility;
            // إضافة التحكم في ظهور واختفاء عناصر SPH, CYL, POW
            if (cmbSphLeft != null) cmbSphLeft.Visibility = visibility;
            if (cmbCylLeft != null) cmbCylLeft.Visibility = visibility;
            if (cmbpowLeft != null) cmbpowLeft.Visibility = visibility;
            if (lblLeftDiscount != null) lblLeftDiscount.Visibility = Visibility.Collapsed;

            // تفريغ جميع الحقول عند إلغاء اختيار العدسة
            if (!isVisible)
            {
                _isLoading = true;
                try
                {
                    if (txtBarCodLeft != null) txtBarCodLeft.Text = "";
                    if (cmbLensCategoryLeft != null) cmbLensCategoryLeft.SelectedIndex = 0;
                    if (cmbLensesNameLeft != null) cmbLensesNameLeft.SelectedItem = null;
                    if (cmbColorLeft != null) cmbColorLeft.SelectedItem = null;
                    if (cmbExpireOnLeft != null) cmbExpireOnLeft.SelectedItem = null;
                    if (cmbPrescriptionLeft != null) cmbPrescriptionLeft.SelectedItem = null;
                    if (txtAxisLeft != null)
                    {
                        txtAxisLeft.Text = "";
                        txtAxisLeft.Visibility = Visibility.Collapsed;
                        txtAxisLeft.IsReadOnly = true;
                    }
                    if (txtAddLeft != null) txtAddLeft.Text = "";
                    if (txtBCLeft != null) txtBCLeft.Text = "";
                    if (txtDiaLeft != null) txtDiaLeft.Text = "";
                    if (txtPriceLeft != null) txtPriceLeft.Text = "";
                    if (txtQuantityLeft != null) txtQuantityLeft.Text = "1";
                    if (cmbSphLeft != null) cmbSphLeft.SelectedItem = null;
                    if (cmbCylLeft != null) cmbCylLeft.SelectedItem = null;
                    if (cmbpowLeft != null) cmbpowLeft.SelectedItem = null;
                    if (lblLeftDiscount != null) lblLeftDiscount.Content = "";
                    if(txtAvailableQuantityLeft != null) txtAvailableQuantityLeft.Text = "0";
                }
                finally
                {
                    _isLoading = false;
                }
            }
        }

        private async void cmbLensCategoryRight_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading || cmbLensCategoryRight?.SelectedItem == null) return;

            _isLoading = true;
            try
            {
                // الحصول على النوع المحدد
                var selectedCategory = cmbLensCategoryRight.SelectedItem as LensCategory;
                if (selectedCategory != null)
                {
                    // تحميل العدسات حسب النوع
                    if (selectedCategory.Id == 0)
                    {
                        // إذا تم اختيار "كل الأنواع"، قم بتحميل جميع العدسات المتوفرة في المخزن
                        cmbLensesNameRight.ItemsSource = _lenses;
                    }
                    else
                    {
                        // تحميل العدسات حسب النوع المحدد والمتوفرة في المخزن
                        var warehouseFilteredLenses = await _lensService.GetLensesByCategoryAndWarehouseAsync(selectedCategory.Id, _warehouseId);
                        cmbLensesNameRight.ItemsSource = warehouseFilteredLenses;
                    }

                    // إعادة تعيين الاختيار
                    cmbLensesNameRight.SelectedItem = null;
                    cmbPrescriptionRight.ItemsSource = null;
                    cmbColorRight.ItemsSource = null;

                    // مسح الحقول المرتبطة وإخفاء حقل Axis
                    if (txtAxisRight != null)
                    {
                        txtAxisRight.Text = "";
                        txtAxisRight.Visibility = Visibility.Collapsed;
                        txtAxisRight.IsReadOnly = true;
                    }
                    if (txtAddRight != null) txtAddRight.Text = "";
                    if (txtBCRight != null) txtBCRight.Text = "";
                    if (txtDiaRight != null) txtDiaRight.Text = "";
                    txtPriceRight.Text = "";
                    lblRightDiscount.Content = "";
                    lblRightDiscount.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل العدسات: {ex.Message}", "خطأ", true);
            }
            finally
            {
                _isLoading = false;
            }
        }

        private async void cmbLensesNameRight_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading || cmbLensesNameRight?.SelectedItem == null) return;

            _isLoading = true;
            try
            {
                // الحصول على العدسة المحددة
                var selectedLens = cmbLensesNameRight.SelectedItem as Lens;
                if (selectedLens != null)
                {
                    // تطبيق منطق Axis للعدسة اليمنى
                    ApplyAxisLogic(selectedLens, isRight: true);

                    // تحديث الحقول الأخرى بناءً على خصائص العدسة
                    if (txtAddRight != null) txtAddRight.Text = selectedLens.Addtion?.ToString() ?? "";
                    if (txtBCRight != null) txtBCRight.Text = selectedLens.BC?.ToString() ?? "";
                    if (txtDiaRight != null) txtDiaRight.Text = selectedLens.Dia?.ToString() ?? "";
                    cmbColorRight.SelectedValue = null;
                    cmbExpireOnRight.SelectedValue = null;
                    txtPriceRight.Text = "";
                    lblRightDiscount.Content = "";
                    lblRightDiscount.Visibility = Visibility.Collapsed;
                    // تحديث رؤية الحقول بناءً على خصائص العدسة
                    if (cmbExpireOnRight != null) cmbExpireOnRight.Visibility = selectedLens.Exp ? Visibility.Visible : Visibility.Collapsed;
                    if (cmbPrescriptionRight != null) cmbPrescriptionRight.Visibility = (selectedLens.Sphere || selectedLens.Cylinder || selectedLens.Power) ? Visibility.Visible : Visibility.Collapsed;
                    if (txtAddRight != null) txtAddRight.Visibility = selectedLens.Addtion.HasValue ? Visibility.Visible : Visibility.Collapsed;
                    if (txtBCRight != null) txtBCRight.Visibility = selectedLens.BC.HasValue ? Visibility.Visible : Visibility.Collapsed;
                    if (txtDiaRight != null) txtDiaRight.Visibility = selectedLens.Dia.HasValue ? Visibility.Visible : Visibility.Collapsed;
                    if (cmbColorRight != null) cmbColorRight.Visibility = /*selectedLens.Color ?*/ Visibility.Visible /*: Visibility.Collapsed*/;

                    // تحديث رؤية حقول SPH, CYL, POW
                    if (cmbSphRight != null) cmbSphRight.Visibility = selectedLens.Sphere ? Visibility.Visible : Visibility.Collapsed;
                    if (cmbCylRight != null) cmbCylRight.Visibility = selectedLens.Cylinder ? Visibility.Visible : Visibility.Collapsed;
                    if (cmbpowRight != null) cmbpowRight.Visibility = selectedLens.Power ? Visibility.Visible : Visibility.Collapsed;
                    // تحميل بيانات الوصفة
                    await LoadPrescriptionData(selectedLens.Id, true);
                }
            }
            finally
            {
                _isLoading = false;
            }
        }

        private async void cmbLensCategoryLeft_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading || cmbLensCategoryLeft?.SelectedItem == null) return;

            _isLoading = true;
            try
            {
                // الحصول على النوع المحدد
                var selectedCategory = cmbLensCategoryLeft.SelectedItem as LensCategory;
                if (selectedCategory != null)
                {
                    // تحميل العدسات حسب النوع
                    if (selectedCategory.Id == 0)
                    {
                        // إذا تم اختيار "كل الأنواع"، قم بتحميل جميع العدسات المتوفرة في المخزن
                        cmbLensesNameLeft.ItemsSource = _lenses;
                    }
                    else
                    {
                        // تحميل العدسات حسب النوع المحدد والمتوفرة في المخزن
                        var warehouseFilteredLenses = await _lensService.GetLensesByCategoryAndWarehouseAsync(selectedCategory.Id, _warehouseId);
                        cmbLensesNameLeft.ItemsSource = warehouseFilteredLenses;
                    }

                    // إعادة تعيين الاختيار
                    cmbLensesNameLeft.SelectedItem = null;
                    cmbPrescriptionLeft.ItemsSource = null;
                    cmbColorLeft.ItemsSource = null;

                    // مسح الحقول المرتبطة وإخفاء حقل Axis
                    if (txtAxisLeft != null)
                    {
                        txtAxisLeft.Text = "";
                        txtAxisLeft.Visibility = Visibility.Collapsed;
                        txtAxisLeft.IsReadOnly = true;
                    }
                    if (txtAddLeft != null) txtAddLeft.Text = "";
                    if (txtBCLeft != null) txtBCLeft.Text = "";
                    if (txtDiaLeft != null) txtDiaLeft.Text = "";
                    txtPriceLeft.Text = "";
                    lblLeftDiscount.Content = "";
                    lblLeftDiscount.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل العدسات: {ex.Message}", "خطأ", true);
            }
            finally
            {
                _isLoading = false;
            }
        }

        private async void cmbLensesNameLeft_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading || cmbLensesNameLeft?.SelectedItem == null) return;

            _isLoading = true;
            try
            {
                // الحصول على العدسة المحددة
                var selectedLens = cmbLensesNameLeft.SelectedItem as Lens;
                if (selectedLens != null)
                {
                    // تطبيق منطق Axis للعدسة اليسرى
                    ApplyAxisLogic(selectedLens, isRight: false);

                    // تحديث الحقول الأخرى بناءً على خصائص العدسة
                    if (txtAddLeft != null) txtAddLeft.Text = selectedLens.Addtion?.ToString() ?? "";
                    if (txtBCLeft != null) txtBCLeft.Text = selectedLens.BC?.ToString() ?? "";
                    if (txtDiaLeft != null) txtDiaLeft.Text = selectedLens.Dia?.ToString() ?? "";
                    cmbColorLeft.SelectedValue = null;
                    cmbExpireOnLeft.SelectedValue = null;
                    txtPriceLeft.Text = "";
                    lblLeftDiscount.Content = "";
                    lblLeftDiscount.Visibility = Visibility.Collapsed;
                    // تحديث رؤية الحقول بناءً على خصائص العدسة
                    if (cmbExpireOnLeft != null) cmbExpireOnLeft.Visibility = selectedLens.Exp ? Visibility.Visible : Visibility.Collapsed;
                    if (cmbPrescriptionLeft != null) cmbPrescriptionLeft.Visibility = (selectedLens.Sphere || selectedLens.Cylinder || selectedLens.Power) ? Visibility.Visible : Visibility.Collapsed;
                    if (txtAddLeft != null) txtAddLeft.Visibility = selectedLens.Addtion.HasValue ? Visibility.Visible : Visibility.Collapsed;
                    if (txtBCLeft != null) txtBCLeft.Visibility = selectedLens.BC.HasValue ? Visibility.Visible : Visibility.Collapsed;
                    if (txtDiaLeft != null) txtDiaLeft.Visibility = selectedLens.Dia.HasValue ? Visibility.Visible : Visibility.Collapsed;
                    if (cmbColorLeft != null) cmbColorLeft.Visibility = /*selectedLens.Color ?*/ Visibility.Visible /*: Visibility.Collapsed*/;

                    // تحديث رؤية حقول SPH, CYL, POW
                    if (cmbSphLeft != null) cmbSphLeft.Visibility = selectedLens.Sphere ? Visibility.Visible : Visibility.Collapsed;
                    if (cmbCylLeft != null) cmbCylLeft.Visibility = selectedLens.Cylinder ? Visibility.Visible : Visibility.Collapsed;
                    if (cmbpowLeft != null) cmbpowLeft.Visibility = selectedLens.Power ? Visibility.Visible : Visibility.Collapsed;

                    // تحميل بيانات الوصفة
                    await LoadPrescriptionData(selectedLens.Id, false);
                }
            }
            finally
            {
                _isLoading = false;
            }
        }

        // دالة موحدة لتحميل بيانات وصفة العدسة لكلا العينين
        private async Task LoadPrescriptionData(int lensId, bool isRight)
        {
            try
            {
                // استرجاع العدسة مع البيانات المرتبطة بها
                _prescriptions = await _lensService.GetLensPrescriptionsByLensIdAsync(lensId);

                if (_prescriptions == null) return;

                // تحديد عناصر التحكم بناءً على العين (يمين/يسار)
                ComboBox prescriptionCombo = isRight ? cmbPrescriptionRight : cmbPrescriptionLeft;
                ComboBox colorCombo = isRight ? cmbColorRight : cmbColorLeft;
                ComboBox sphCombo = isRight ? cmbSphRight : cmbSphLeft;
                ComboBox cylCombo = isRight ? cmbCylRight : cmbCylLeft;
                ComboBox powCombo = isRight ? cmbpowRight : cmbpowLeft;

                // تحميل بيانات الوصفة (Sph/Cyl/Pow) في القائمة المنسدلة
                var prescriptions = _prescriptions
                    .Select(lp => new
                    {
                        Id = lp.Id,
                        Text = FormatPrescription(lp.Sphere, lp.Cylinder, lp.Pow)
                    })
                    .OrderBy(p => p.Text)
                    .ToList();

                prescriptionCombo.ItemsSource = prescriptions;
                prescriptionCombo.DisplayMemberPath = "Text";
                prescriptionCombo.SelectedValuePath = "Id";

                // استخراج قيم فريدة لـ SPH, CYL, POW من جدول Prescription
                var sphValues = _prescriptions
                    .Where(p => p.Sphere != null)
                    .Select(p => p.Sphere.Value)
                    .Distinct()
                    .OrderBy(v => v)
                    .ToList();

                var cylValues = _prescriptions
                    .Where(p => p.Cylinder != null)
                    .Select(p => p.Cylinder.Value)
                    .Distinct()
                    .OrderBy(v => v)
                    .ToList();

                var powValues = _prescriptions
                    .Where(p => p.Pow != null)
                    .Select(p => p.Pow.Value)
                    .Distinct()
                    .OrderBy(v => v)
                    .ToList();

                // تعبئة القوائم المنسدلة الفردية مع عرض القيم المنسقة
                sphCombo.ItemsSource = null; // Clear first to avoid binding issues
                cylCombo.ItemsSource = null;
                powCombo.ItemsSource = null;

                // Create custom display for combo boxes
                var sphDisplayItems = sphValues.Select(v => new { Value = v, Display = v == 0 ? "PL" : v.ToString() }).ToList();
                var cylDisplayItems = cylValues.Select(v => new { Value = v, Display = v == 0 ? "PL" : v.ToString() }).ToList();
                var powDisplayItems = powValues.Select(v => new { Value = v, Display = v == 0 ? "PL" : v.ToString() }).ToList();

                // Set the ItemsSource, DisplayMemberPath, and SelectedValuePath
                sphCombo.ItemsSource = sphDisplayItems;
                sphCombo.DisplayMemberPath = "Display";
                sphCombo.SelectedValuePath = "Value";

                cylCombo.ItemsSource = cylDisplayItems;
                cylCombo.DisplayMemberPath = "Display";
                cylCombo.SelectedValuePath = "Value";

                powCombo.ItemsSource = powDisplayItems;
                powCombo.DisplayMemberPath = "Display";
                powCombo.SelectedValuePath = "Value";
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات الوصفة: {ex.Message}", "خطأ", true);
            }
        }

        // دالة مساعدة لتنسيق بيانات الوصفة
        private string FormatPrescription(Prescription? sph, Prescription? cyl, Prescription? pow)
        {
            var parts = new List<string>();

            if (sph != null) parts.Add($"SPH: {(sph.Value == 0 ? "PL" : sph.Value.ToString("F2"))}");
            if (cyl != null) parts.Add($"CYL: {(cyl.Value == 0 ? "PL" : cyl.Value.ToString("F2"))}");
            if (pow != null) parts.Add($"POW: {(pow.Value == 0 ? "PL" : pow.Value.ToString("F2"))}");

            return string.Join(" / ", parts);
        }

        // دالة لتطبيق منطق Axis للعدسة
        private void ApplyAxisLogic(Lens lens, bool isRight)
        {
            TextBox axisTextBox = isRight ? txtAxisRight : txtAxisLeft;

            if (lens.Axis.HasValue)
            {
                // إذا كانت العدسة لها قيمة Axis محددة
                axisTextBox.Text = lens.Axis.Value.ToString();
                axisTextBox.Visibility = Visibility.Visible;
                axisTextBox.IsReadOnly = false;
            }
            else
            {
                // إذا لم تكن العدسة لها قيمة Axis
                axisTextBox.Text = "";
                axisTextBox.Visibility = Visibility.Collapsed;
                axisTextBox.IsReadOnly = true;
            }
        }

        // دالة للتحقق من صحة حقل Axis
        private bool ValidateAxisField(bool isRight, out string errorMessage)
        {
            errorMessage = string.Empty;
            TextBox axisTextBox = isRight ? txtAxisRight : txtAxisLeft;
            string eyeSide = isRight ? "اليمنى" : "اليسرى";

            // إذا كان الحقل مرئياً (مطلوب)
            if (axisTextBox.Visibility == Visibility.Visible)
            {
                if (string.IsNullOrWhiteSpace(axisTextBox.Text))
                {
                    errorMessage = $"يرجى إدخال قيمة Axis للعدسة {eyeSide}";
                    return false;
                }

                if (!decimal.TryParse(axisTextBox.Text, out decimal axisValue))
                {
                    errorMessage = $"يرجى إدخال قيمة صحيحة لـ Axis للعدسة {eyeSide}";
                    return false;
                }
            }

            return true;
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnAdd.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnAdd.IsEnabled = true;
        }

        private async void btnAdd_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من اختيار عدسة واحدة على الأقل
                if (!chkRghtLen.IsChecked.HasValue && !chkLeftLen.IsChecked.HasValue)
                {
                    ErrorBox.Show("يجب اختيار عدسة واحدة على الأقل", "تنبيه", false);
                    return;
                }

                var selectedLenses = new List<SaleItemVM>();

                // إنشاء معرف مشترك للعدسات المضافة معًا
                Guid? pairId = null;

                // إذا تم اختيار كلا العدسات، سنقوم بإنشاء معرف مشترك
                if (chkRghtLen.IsChecked == true && chkLeftLen.IsChecked == true)
                {
                    pairId = Guid.NewGuid();
                }

                // إضافة العدسة اليمنى إذا كانت مختارة
                if (chkRghtLen.IsChecked == true)
                {
                    if (cmbLensesNameRight.SelectedItem == null)
                    {
                        ErrorBox.Show("يرجى اختيار نوع العدسة اليمنى", "بيانات ناقصة", false);
                        return;
                    }

                    if (cmbPrescriptionRight.Visibility == Visibility.Visible && cmbPrescriptionRight.SelectedItem == null)
                    {
                        ErrorBox.Show("يرجى اختيار وصفة العدسة اليمنى", "بيانات ناقصة", false);
                        return;
                    }

                    if (cmbColorRight.Visibility == Visibility.Visible && cmbColorRight.SelectedItem == null)
                    {
                        ErrorBox.Show("يرجى اختيار لون العدسة اليمنى", "بيانات ناقصة", false);
                        return;
                    }

                    if (cmbExpireOnRight.Visibility == Visibility.Visible && cmbExpireOnRight.SelectedItem == null)
                    {
                        ErrorBox.Show("يرجى اختيار تاريخ صلاحية العدسة اليمنى", "بيانات ناقصة", false);
                        return;
                    }
                    else if (cmbExpireOnRight.SelectedIndex == -1)
                    {
                        ErrorBox.Show("لا توجد كمية في العدسة", "بيانات ناقصة", false);
                        return;
                    }

                    if (string.IsNullOrEmpty(txtPriceRight.Text) || !decimal.TryParse(txtPriceRight.Text, out decimal priceRight))
                    {
                        ErrorBox.Show("يرجى إدخال سعر صحيح للعدسة اليمنى", "بيانات ناقصة", false);
                        return;
                    }

                    if (string.IsNullOrEmpty(txtQuantityRight.Text) || !int.TryParse(txtQuantityRight.Text, out int quantityRight) || quantityRight <= 0)
                    {
                        ErrorBox.Show("يجب أن تكون كمية العدسة اليمنى أكبر من 0", "بيانات ناقصة", false);
                        return;
                    }

                    // التحقق من صحة حقل Axis للعدسة اليمنى
                    if (!ValidateAxisField(isRight: true, out string rightAxisError))
                    {
                        ErrorBox.Show(rightAxisError, "بيانات ناقصة", false);
                        return;
                    }

                    // تحضير وصف العدسة ليشمل الخصائص والأرقام
                    string lensSpecs = "";
                    if (cmbPrescriptionRight.Visibility == Visibility.Visible && cmbPrescriptionRight.SelectedItem != null)
                    {
                        lensSpecs += cmbPrescriptionRight.Text + " | ";
                    }

                    if (txtAxisRight.Visibility == Visibility.Visible && !string.IsNullOrEmpty(txtAxisRight.Text))
                    {
                        lensSpecs += "Axis:" + txtAxisRight.Text + " | ";
                    }

                    if (txtBCRight.Visibility == Visibility.Visible && !string.IsNullOrEmpty(txtBCRight.Text))
                    {
                        lensSpecs += "BC:" + txtBCRight.Text + " | ";
                    }

                    if (txtDiaRight.Visibility == Visibility.Visible && !string.IsNullOrEmpty(txtDiaRight.Text))
                    {
                        lensSpecs += "Dia:" + txtDiaRight.Text + " | ";
                    }

                    // إزالة الـ " | " الأخير إذا كان موجوداً
                    if (lensSpecs.EndsWith(" | "))
                    {
                        lensSpecs = lensSpecs.Substring(0, lensSpecs.Length - 3);
                    }

                    // إنشاء اسم مركب يتضمن اسم العدسة وخصائصها
                    string lensName = cmbLensesNameRight.Text+" | "+cmbLensCategoryRight.Text;
                    string fullLensName = lensName;
                    if (!string.IsNullOrEmpty(lensSpecs))
                    {
                        fullLensName = $"{lensName} - {lensSpecs} - يمين";
                    }

                    // Get discount information for right lens
                    decimal originalPriceRight = 0;
                    decimal discountRight = 0;

                    // Check if there's a discount
                    if (lblRightDiscount.Visibility == Visibility.Visible)
                    {
                        // Get the original price from the lens prescription
                        var lensPrescriptionRight = _prescriptions?.FirstOrDefault(lp =>
                            lp.Id == (int)cmbPrescriptionRight.SelectedValue);

                        if (lensPrescriptionRight != null)
                        {
                            originalPriceRight = lensPrescriptionRight.SellPrice;
                            discountRight = originalPriceRight - priceRight;
                        }
                        else
                        {
                            originalPriceRight = priceRight;
                        }
                    }
                    else
                    {
                        originalPriceRight = priceRight;
                    }

                    // إنشاء كائن SaleItemVM للعدسة اليمنى
                    var rightLens = new SaleItemVM
                    {
                        Type = "عدسة",
                        Name = fullLensName,
                        OriginalPrice = originalPriceRight,
                        Discount = discountRight,
                        SellPrice = priceRight,
                        Quantity = quantityRight,
                        LensQuantityRightId = cmbExpireOnRight.SelectedValue != null ? (int)cmbExpireOnRight.SelectedValue : null,
                        ColorName = cmbColorRight.Visibility == Visibility.Visible ? cmbColorRight.Text : null,
                        Axis = decimal.TryParse(txtAxisRight.Text, out decimal axis) ? axis : (decimal?)null,
                        PairId = pairId // إضافة معرف الزوج
                    };

                    selectedLenses.Add(rightLens);
                }

                // إضافة العدسة اليسرى إذا كانت مختارة
                if (chkLeftLen.IsChecked == true)
                {
                    if (cmbLensesNameLeft.SelectedItem == null)
                    {
                        ErrorBox.Show("يرجى اختيار نوع العدسة اليسرى", "بيانات ناقصة", false);
                        return;
                    }

                    if (cmbPrescriptionLeft.Visibility == Visibility.Visible && cmbPrescriptionLeft.SelectedItem == null)
                    {
                        ErrorBox.Show("يرجى اختيار وصفة العدسة اليسرى", "بيانات ناقصة", false);
                        return;
                    }

                    if (cmbColorLeft.Visibility == Visibility.Visible && cmbColorLeft.SelectedItem == null)
                    {
                        ErrorBox.Show("يرجى اختيار لون العدسة اليسرى", "بيانات ناقصة", false);
                        return;
                    }
                    var len = cmbLensesNameRight.SelectedItem as Lens;
                    if (cmbExpireOnLeft.Visibility == Visibility.Visible && cmbExpireOnLeft.SelectedItem == null)
                    {
                        ErrorBox.Show("يرجى اختيار تاريخ صلاحية العدسة اليسرى", "بيانات ناقصة", false);
                        return;
                    }
                    else if (cmbExpireOnLeft.SelectedIndex == -1)
                    {
                        ErrorBox.Show("لا توجد كمية في العدسة", "بيانات ناقصة", false);
                        return;
                    }

                    if (string.IsNullOrEmpty(txtPriceLeft.Text) || !decimal.TryParse(txtPriceLeft.Text, out decimal priceLeft))
                    {
                        ErrorBox.Show("يرجى إدخال سعر صحيح للعدسة اليسرى", "بيانات ناقصة", false);
                        return;
                    }

                    if (string.IsNullOrEmpty(txtQuantityLeft.Text) || !int.TryParse(txtQuantityLeft.Text, out int quantityLeft) || quantityLeft <= 0)
                    {
                        ErrorBox.Show("يجب أن تكون كمية العدسة اليسرى أكبر من 0", "بيانات ناقصة", false);
                        return;
                    }

                    // التحقق من صحة حقل Axis للعدسة اليسرى
                    if (!ValidateAxisField(isRight: false, out string leftAxisError))
                    {
                        ErrorBox.Show(leftAxisError, "بيانات ناقصة", false);
                        return;
                    }
                    // تحضير وصف العدسة ليشمل الخصائص والأرقام
                    string lensSpecs = "";
                    if (cmbPrescriptionLeft.Visibility == Visibility.Visible && cmbPrescriptionLeft.SelectedItem != null)
                    {
                        lensSpecs += cmbPrescriptionLeft.Text + " | ";
                    }

                    if (txtAxisLeft.Visibility == Visibility.Visible && !string.IsNullOrEmpty(txtAxisLeft.Text))
                    {
                        lensSpecs += "Axis:" + txtAxisLeft.Text + " | ";
                    }

                    if (txtBCLeft.Visibility == Visibility.Visible && !string.IsNullOrEmpty(txtBCLeft.Text))
                    {
                        lensSpecs += "BC:" + txtBCLeft.Text + " | ";
                    }

                    if (txtDiaLeft.Visibility == Visibility.Visible && !string.IsNullOrEmpty(txtDiaLeft.Text))
                    {
                        lensSpecs += "Dia:" + txtDiaLeft.Text + " | ";
                    }
                    // إزالة الـ " | " الأخير إذا كان موجوداً
                    if (lensSpecs.EndsWith(" | "))
                    {
                        lensSpecs = lensSpecs.Substring(0, lensSpecs.Length - 3);
                    }

                    // إنشاء اسم مركب يتضمن اسم العدسة وخصائصها
                    string lensName = cmbLensesNameLeft.Text+" | "+cmbLensCategoryLeft.Text;
                    string fullLensName = lensName;
                    if (!string.IsNullOrEmpty(lensSpecs))
                    {
                        fullLensName = $"{lensName} - {lensSpecs} - يسار";
                    }

                    // Get discount information for left lens
                    decimal originalPriceLeft = 0;
                    decimal discountLeft = 0;

                    // Check if there's a discount
                    if (lblLeftDiscount.Visibility == Visibility.Visible)
                    {
                        // Get the original price from the lens prescription
                        var lensPrescriptionLeft = _prescriptions?.FirstOrDefault(lp =>
                            lp.Id == (int)cmbPrescriptionLeft.SelectedValue);

                        if (lensPrescriptionLeft != null)
                        {
                            originalPriceLeft = lensPrescriptionLeft.SellPrice;
                            discountLeft = originalPriceLeft - priceLeft;
                        }
                        else
                        {
                            originalPriceLeft = priceLeft;
                        }
                    }
                    else
                    {
                        originalPriceLeft = priceLeft;
                    }

                    // إنشاء كائن SaleItemVM للعدسة اليسرى
                    var leftLens = new SaleItemVM
                    {
                        Type = "عدسة",
                        Name = fullLensName,
                        OriginalPrice = originalPriceLeft,
                        Discount = discountLeft,
                        SellPrice = priceLeft,
                        Quantity = quantityLeft,
                        LensQuantityLeftId = cmbExpireOnLeft.SelectedValue != null ? (int)cmbExpireOnLeft.SelectedValue : null,
                        ColorName = cmbColorLeft.Visibility == Visibility.Visible ? cmbColorLeft.Text : null,
                        Axis = decimal.TryParse(txtAxisLeft.Text, out decimal axis) ? axis : (decimal?)null,
                        PairId = pairId // إضافة معرف الزوج
                    };

                    selectedLenses.Add(leftLens);
                }

                // إذا وصلنا إلى هنا، فقد نجحت عملية التحقق
                // تعيين البيانات كنتيجة حوار وإغلاق النافذة
                this.DialogResult = true;
                this.Tag = selectedLenses;
                this.Close();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء إضافة العدسات: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }

        private async void cmbColorRight_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (_isLoading || cmbColorRight.SelectedItem == null) return;

                // عند اختيار لون، قم بتحميل تواريخ الصلاحية المتاحة لهذا اللون
                if (cmbColorRight.SelectedValue is int lensColorId)
                {
                    // تحميل تواريخ الصلاحية للون المحدد
                    _isLoading = true;
                    await LoadExpirationDatesForColor(lensColorId, isRight: true);

                    // حساب إجمالي الكمية المتاحة لهذا اللون من جميع تواريخ الصلاحية في المخزن المختار
                    int totalAvailable = 0;
                    if (cmbExpireOnRight.ItemsSource is IEnumerable<dynamic> expirationItems)
                    {
                        totalAvailable = expirationItems.Sum(item => (int)item.Quantity);
                    }

                    // عرض الكمية المتاحة وتغيير اللون بناءً على الكمية
                    txtAvailableQuantityRight.Text = totalAvailable.ToString();
                    if (totalAvailable <= 0)
                    {
                        txtAvailableQuantityRight.Foreground = Brushes.Red;
                    }
                    else if (totalAvailable <= 5)
                    {
                        txtAvailableQuantityRight.Foreground = Brushes.Orange;
                    }
                    else
                    {
                        txtAvailableQuantityRight.Foreground = Brushes.Green;
                    }

                    _isLoading = false;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل تواريخ الصلاحية: {ex.Message}", "خطأ", true);
            }
        }

        private async void cmbColorLeft_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (_isLoading || cmbColorLeft.SelectedItem == null) return;

                // عند اختيار لون، قم بتحميل تواريخ الصلاحية المتاحة لهذا اللون
                if (cmbColorLeft.SelectedValue is int lensColorId)
                {
                    // تحميل تواريخ الصلاحية للون المحدد
                    _isLoading = true;
                    await LoadExpirationDatesForColor(lensColorId, isRight: false);

                    // حساب إجمالي الكمية المتاحة لهذا اللون من جميع تواريخ الصلاحية في المخزن المختار
                    int totalAvailable = 0;
                    if (cmbExpireOnLeft.ItemsSource is IEnumerable<dynamic> expirationItems)
                    {
                        totalAvailable = expirationItems.Sum(item => (int)item.Quantity);
                    }

                    // عرض الكمية المتاحة وتغيير اللون بناءً على الكمية
                    txtAvailableQuantityLeft.Text = totalAvailable.ToString();
                    if (totalAvailable <= 0)
                    {
                        txtAvailableQuantityLeft.Foreground = Brushes.Red;
                    }
                    else if (totalAvailable <= 5)
                    {
                        txtAvailableQuantityLeft.Foreground = Brushes.Orange;
                    }
                    else
                    {
                        txtAvailableQuantityLeft.Foreground = Brushes.Green;
                    }

                    _isLoading = false;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل تواريخ الصلاحية: {ex.Message}", "خطأ", true);
            }
        }

        // دالة لمعالجة حدث تغيير الاختيار في cmbPrescriptionRight
        private async void cmbPrescriptionRight_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // ...
            if (_isLoading || cmbPrescriptionRight?.SelectedItem == null) return;

            _isLoading = true;
            try
            {
                // الحصول على معرف الوصفة المختارة
                if (cmbPrescriptionRight.SelectedValue is int prescriptionId)
                {
                    // تحديث القيم في ComboBox الفردية
                    var selectedPrescription = _prescriptions?.FirstOrDefault(p => p.Id == prescriptionId);
                    if (selectedPrescription != null)
                    {
                        if (selectedPrescription.Sphere != null)
                        {
                            cmbSphRight.SelectedItem = cmbSphRight.Items.OfType<dynamic>()
                                .FirstOrDefault(item => item.Value == selectedPrescription.Sphere.Value);
                            //// Find the item with matching Value property
                            //foreach (var item in cmbSphRight.Items)
                            //{
                            //    try
                            //    {
                            //        dynamic obj = item;
                            //        if (obj.Value == selectedPrescription.Sphere.Value)
                            //        {
                            //            cmbSphRight.SelectedItem = item;
                            //            break;
                            //        }
                            //    }
                            //    catch
                            //    {
                            //        continue;
                            //    }
                            //}
                        }

                        if (selectedPrescription.Cylinder != null)
                        {
                            cmbCylRight.SelectedItem = cmbCylRight.Items.OfType<dynamic>()
                                .FirstOrDefault(item => item.Value == selectedPrescription.Cylinder.Value);
                            // Find the item with matching Value property
                            //foreach (var item in cmbCylRight.Items)
                            //{
                            //    try
                            //    {
                            //        dynamic obj = item;
                            //        if (obj.Value == selectedPrescription.Cylinder.Value)
                            //        {
                            //            cmbCylRight.SelectedItem = item;
                            //            break;
                            //        }
                            //    }
                            //    catch
                            //    {
                            //        continue;
                            //    }
                            //}
                        }

                        if (selectedPrescription.Pow != null)
                        {
                            // Find the item with matching Value property (محسنة)
                            cmbpowRight.SelectedItem = cmbpowRight.Items
                                .OfType<object>()
                                .FirstOrDefault(item =>
                                {
                                    try
                                    {
                                        dynamic obj = item;
                                        return obj.Value == selectedPrescription.Pow.Value;
                                    }
                                    catch
                                    {
                                        return false;
                                    }
                                });
                        }
                    }

                    await LoadColorsForPrescription(prescriptionId, true);
                }
            }
            finally
            {
                _isLoading = false;
            }
        }

        // دالة لمعالجة حدث تغيير الاختيار في cmbPrescriptionLeft
        private async void cmbPrescriptionLeft_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading || cmbPrescriptionLeft?.SelectedItem == null) return;

            _isLoading = true;
            try
            {
                // الحصول على معرف الوصفة المختارة
                if (cmbPrescriptionLeft.SelectedValue is int prescriptionId)
                {
                    // تحديث القيم في ComboBox الفردية
                    var selectedPrescription = _prescriptions?.FirstOrDefault(p => p.Id == prescriptionId);
                    if (selectedPrescription != null)
                    {
                        if (selectedPrescription.Sphere != null)
                        {
                            // Find the item with matching Value property (محسنة)
                            cmbSphLeft.SelectedItem = cmbSphLeft.Items
                                .OfType<object>()
                                .FirstOrDefault(item =>
                                {
                                    try
                                    {
                                        dynamic obj = item;
                                        return obj.Value == selectedPrescription.Sphere.Value;
                                    }
                                    catch
                                    {
                                        return false;
                                    }
                                });
                        }

                        if (selectedPrescription.Cylinder != null)
                        {
                            // Find the item with matching Value property (محسنة)
                            cmbCylLeft.SelectedItem = cmbCylLeft.Items
                                .OfType<object>()
                                .FirstOrDefault(item =>
                                {
                                    try
                                    {
                                        dynamic obj = item;
                                        return obj.Value == selectedPrescription.Cylinder.Value;
                                    }
                                    catch
                                    {
                                        return false;
                                    }
                                });
                        }

                        if (selectedPrescription.Pow != null)
                        {
                            // Find the item with matching Value property (محسنة)
                            cmbpowLeft.SelectedItem = cmbpowLeft.Items
                                .OfType<object>()
                                .FirstOrDefault(item =>
                                {
                                    try
                                    {
                                        dynamic obj = item;
                                        return obj.Value == selectedPrescription.Pow.Value;
                                    }
                                    catch
                                    {
                                        return false;
                                    }
                                });
                        }
                    }

                    await LoadColorsForPrescription(prescriptionId, false);
                }
            }
            finally
            {
                _isLoading = false;
            }
        }

        // دالة لتحميل الألوان المرتبطة بالوصفة المحددة
        private async Task LoadColorsForPrescription(int prescriptionId, bool isRight)
        {
            try
            {
                // استرجاع الوصفة مع البيانات المرتبطة بها
                var prescription = _prescriptions
                    .FirstOrDefault(lp => lp.Id == prescriptionId);

                if (prescription == null) return;
                TextBox txtPrice = isRight ? txtPriceRight : txtPriceLeft;
                // تحديد عنصر التحكم بناءً على العين (يمين/يسار)
                ComboBox colorCombo = isRight ? cmbColorRight : cmbColorLeft;

                // تعبئة الألوان المتاحة للوصفة المحددة
                if (colorCombo != null)
                {
                    // التحقق من وجود تخفيض ساري للعدسة
                    decimal originalPrice = prescription.SellPrice;
                    var (hasDiscount, discountPercentage) = await _discountManager.CheckLensDiscount(prescription.LensId.Value, DateTime.Now);

                    if (hasDiscount)
                    {
                        // حساب السعر بعد التخفيض
                        decimal discountedPrice = _discountManager.CalculateDiscountedPrice(originalPrice, discountPercentage);

                        // عرض السعر بعد التخفيض
                        txtPrice.Text = discountedPrice.ToString("N3");

                        // إظهار رسالة للمستخدم بوجود تخفيض
                        Label discountLabel = isRight ? lblRightDiscount : lblLeftDiscount;
                        if (discountLabel != null)
                        {
                            discountLabel.Visibility = Visibility.Visible;
                            discountLabel.Content = $"يوجد تخفيض {discountPercentage}% - السعر الأصلي: {originalPrice:N3}";
                        }
                    }
                    else
                    {
                        // عرض السعر الأصلي
                        txtPrice.Text = originalPrice.ToString("N3");

                        // إخفاء رسالة التخفيض
                        Label discountLabel = isRight ? lblRightDiscount : lblLeftDiscount;
                        if (discountLabel != null) discountLabel.Visibility = Visibility.Collapsed;
                    }
                    var lensPrescriptionColors = await _lensService.GetLensPrescriptionColorsByLensPrescriptionIdAsync(prescriptionId);

                    if (lensPrescriptionColors.Any())
                    {
                        colorCombo.ItemsSource = lensPrescriptionColors;
                        colorCombo.DisplayMemberPath = "Color.Name";
                        colorCombo.SelectedValuePath = "Id";
                        colorCombo.SelectedIndex = 0;
                        _isLoading = false;
                        if (isRight) cmbColorRight_SelectionChanged(null, null);else cmbColorLeft_SelectionChanged(null, null);

                    }
                    else
                    {
                        colorCombo.ItemsSource = null;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل الألوان المتاحة: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadExpirationDatesForColor(int colorPrescriptionId, bool isRight)
        {
            try
            {
                // استرجاع كميات العدسات المرتبطة باللون المختار
                var allQuantities = await _lensService.GetLensQuantitiesByColorPrescriptionIdAsync(colorPrescriptionId);

                if (allQuantities == null || !allQuantities.Any()) return;

                // تصفية الكميات حسب المخزن المختار
                var quantities = allQuantities.Where(lq => lq.WarehouseId == _warehouseId).ToList();

                if (!quantities.Any()) return;

                // تحديد عنصر التحكم بناءً على العين (يمين/يسار)
                ComboBox expirationCombo = isRight ? cmbExpireOnRight : cmbExpireOnLeft;

                // تعبئة تواريخ الصلاحية المتاحة للون المحدد


                if (expirationCombo != null)
                {
                    var expirationItems = quantities
                        .Select(q => new
                        {
                            Id = q.Id,
                            Text = q.Exp?.ToString("yyyy-MM-dd") ?? "لا يوجد",
                            Quantity = q.Quantity
                        })
                        .ToList();
                    Lens lens = isRight ? cmbLensesNameRight.SelectedItem as Lens : cmbLensesNameLeft.SelectedItem as Lens;

                    if (expirationItems.Any())
                    {
                        expirationCombo.ItemsSource = expirationItems;
                        expirationCombo.DisplayMemberPath = "Text";
                        expirationCombo.SelectedValuePath = "Id";
                        expirationCombo.SelectedIndex = lens.Exp ? -1 : 0; // اختيار أول تاريخ صلاحية متاح
                        expirationCombo.Visibility = lens.Exp ? Visibility.Visible : Visibility.Collapsed;
                    }
                    else
                    {
                        ErrorBox.Show("لا توجد كمية في العدسة", "بيانات ناقصة", false);
                    }
                }


            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل تواريخ الصلاحية المتاحة: {ex.Message}", "خطأ", true);
            }
        }

        // Process barcode when Enter key is pressed
        private async void TxtBarCod_KeyDown(object sender, KeyEventArgs e)
        {
            if (_isLoading) return;
            if (e.Key == Key.Enter)
            {
                e.Handled = true; // Prevent the event from bubbling up

                TextBox textBox = sender as TextBox;
                if (textBox != null && !string.IsNullOrWhiteSpace(textBox.Text))
                {
                    string barcode = textBox.Text.Trim();
                    bool isRightLens = textBox == txtBarCodRight;

                    await ProcessBarcode(barcode, isRightLens);
                }
            }
        }

        //  Process the barcode and load lens data

        private async Task ProcessBarcode(string barcode, bool isRightLens)
        {
            try
            {
                // Find lens prescription by barcode with warehouse validation
                var lensPrescriptionColor = await _lensService.GetLensPrescriptionColorByBarcodeAndWarehouseAsync(barcode, _warehouseId);

                if (lensPrescriptionColor == null)
                {
                    ErrorBox.Show("لم يتم العثور على عدسة بهذا الباركود في المخزن المختار", "غير متوفر", true);
                    return;
                }

                _isLoading = true;

                try
                {
                    // Make sure the appropriate checkbox is checked
                    if (isRightLens)
                    {
                        chkRghtLen.IsChecked = true;
                        UpdateRightLensVisibility(true);
                    }
                    else
                    {
                        chkLeftLen.IsChecked = true;
                        UpdateLeftLensVisibility(true);
                    }

                    // Get references to the appropriate controls based on lens side

                    ComboBox lensCombo = isRightLens ? cmbLensesNameRight : cmbLensesNameLeft;
                    ComboBox prescriptionCombo = isRightLens ? cmbPrescriptionRight : cmbPrescriptionLeft;
                    ComboBox colorCombo = isRightLens ? cmbColorRight : cmbColorLeft;
                    ComboBox expCombo = isRightLens ? cmbExpireOnRight : cmbExpireOnLeft;
                    ComboBox sphCombo = isRightLens ? cmbSphRight : cmbSphLeft;
                    ComboBox cylCombo = isRightLens ? cmbCylRight : cmbCylLeft;
                    ComboBox powCombo = isRightLens ? cmbpowRight : cmbpowLeft;
                    TextBox priceTextBox = isRightLens ? txtPriceRight : txtPriceLeft;
                    TextBox axisTextBox = isRightLens ? txtAxisRight : txtAxisLeft;
                    TextBox addTextBox = isRightLens ? txtAddRight : txtAddLeft;
                    TextBox bcTextBox = isRightLens ? txtBCRight : txtBCLeft;
                    TextBox diaTextBox = isRightLens ? txtDiaRight : txtDiaLeft;

                    // Set lens selection
                    var lens = lensPrescriptionColor.LensPrescription?.Lens;
                    if (lens != null)
                    {
                        // First select the lens in the dropdown
                        lensCombo.SelectedItem = lensCombo.Items
                            .OfType<Lens>()
                            .FirstOrDefault(l => l.Id == lens.Id);
                        //foreach (Lens item in lensCombo.Items)
                        //{
                        //    if (item.Id == lens.Id)
                        //    {
                        //        lensCombo.SelectedItem = item;
                        //        break;
                        //    }
                        //}

                        // تطبيق منطق Axis للعدسة
                        ApplyAxisLogic(lens, isRightLens);

                        // Update other fields based on lens properties
                        addTextBox.Text = lens.Addtion?.ToString() ?? "";
                        bcTextBox.Text = lens.BC?.ToString() ?? "";
                        diaTextBox.Text = lens.Dia?.ToString() ?? "";

                        // Update visibility of fields
                        if (prescriptionCombo != null) prescriptionCombo.Visibility = (lens.Sphere || lens.Cylinder || lens.Power) ? Visibility.Visible : Visibility.Collapsed;
                        if (addTextBox != null) addTextBox.Visibility = lens.Addtion.HasValue ? Visibility.Visible : Visibility.Collapsed;
                        if (bcTextBox != null) bcTextBox.Visibility = lens.BC.HasValue ? Visibility.Visible : Visibility.Collapsed;
                        if (diaTextBox != null) diaTextBox.Visibility = lens.Dia.HasValue ? Visibility.Visible : Visibility.Collapsed;
                        if (colorCombo != null) colorCombo.Visibility = /*lens.Color ?*/ Visibility.Visible /*: Visibility.Collapsed*/;

                        // تحديث رؤية حقول SPH, CYL, POW
                        if (sphCombo != null) sphCombo.Visibility = lens.Sphere ? Visibility.Visible : Visibility.Collapsed;
                        if (cylCombo != null) cylCombo.Visibility = lens.Cylinder ? Visibility.Visible : Visibility.Collapsed;
                        if (powCombo != null) powCombo.Visibility = lens.Power ? Visibility.Visible : Visibility.Collapsed;
                    }

                    // Load prescriptions for this lens
                    if (lens != null)
                    {

                        await LoadPrescriptionData(lens.Id, isRightLens);

                        // Select the prescription
                        if (lensPrescriptionColor.LensPrescription != null)
                        {
                            // تحديد القيم المقابلة في كل ComboBox
                            var prescription = lensPrescriptionColor.LensPrescription;

                            // تحديد الوصفة في القائمة المنسدلة الرئيسية
                            prescriptionCombo.SelectedItem = prescriptionCombo.Items.OfType<LensPrescription>()
                                .FirstOrDefault(p => p.Id == prescription.Id);
                            //foreach (var item in prescriptionCombo.Items)
                            //{
                            //    try
                            //    {
                            //        // Use dynamic without pattern matching
                            //        dynamic obj = item;
                            //        if (obj.Id == prescription.Id)
                            //        {
                            //            prescriptionCombo.SelectedItem = item;
                            //            break;
                            //        }
                            //    }
                            //    catch
                            //    {
                            //        // Skip if item doesn't have Id property
                            //        continue;
                            //    }
                            //}

                            // تحديد القيم في ComboBox الفردية

                            if (prescription.Sphere != null)
                            {
                                sphCombo.SelectedItem = sphCombo.Items.OfType<LensPrescription>()
                                    .FirstOrDefault(s => s.Id == prescription.Sphere.Value);
                                // Find the item with matching Value property
                                //foreach (var item in sphCombo.Items)
                                //{
                                //    try
                                //    {
                                //        dynamic obj = item;
                                //        if (obj.Value == prescription.Sphere.Value)
                                //        {
                                //            sphCombo.SelectedItem = item;
                                //            break;
                                //        }
                                //    }
                                //    catch
                                //    {
                                //        continue;
                                //    }
                                //}
                            }

                            if (prescription.Cylinder != null)
                            {
                                // Find the item with matching Value property
                                cylCombo.SelectedItem = cylCombo.Items.OfType<LensPrescription>()
                                    .FirstOrDefault(c => c.Id == prescription.Cylinder.Value);
                                //foreach (var item in cylCombo.Items)
                                //{
                                //    try
                                //    {
                                //        dynamic obj = item;
                                //        if (obj.Value == prescription.Cylinder.Value)
                                //        {
                                //            cylCombo.SelectedItem = item;
                                //            break;
                                //        }
                                //    }
                                //    catch
                                //    {
                                //        continue;
                                //    }
                                //}
                            }

                            if (prescription.Pow != null)
                            {
                                // Find the item with matching Value property
                                powCombo.SelectedItem = powCombo.Items.OfType<LensPrescription>()
                                    .FirstOrDefault(p => p.Id == prescription.Pow.Value);
                                //foreach (var item in powCombo.Items)
                                //{
                                //    try
                                //    {
                                //        dynamic obj = item;
                                //        if (obj.Value == prescription.Pow.Value)
                                //        {
                                //            powCombo.SelectedItem = item;
                                //            break;
                                //        }
                                //    }
                                //    catch
                                //    {
                                //        continue;
                                //    }
                                //}
                            }

                            // Load colors for this prescription
                            await LoadColorsForPrescription(lensPrescriptionColor.LensPrescription.Id, isRightLens);

                            // Set price with discount check
                            decimal originalPrice = prescription.SellPrice;
                            var (hasDiscount, discountPercentage) = await _discountManager.CheckLensDiscount(prescription.LensId.Value, DateTime.Now);

                            if (hasDiscount)
                            {
                                // Calculate discounted price
                                decimal discountedPrice = _discountManager.CalculateDiscountedPrice(originalPrice, discountPercentage);
                                priceTextBox.Text = discountedPrice.ToString("N3");

                                // Show discount message
                                Label discountLabel = isRightLens ? lblRightDiscount : lblLeftDiscount;
                                if (discountLabel != null)
                                {
                                    discountLabel.Visibility = Visibility.Visible;
                                    discountLabel.Content = $"يوجد تخفيض {discountPercentage}% - السعر الأصلي: {originalPrice:N3}";
                                }
                            }
                            else
                            {
                                priceTextBox.Text = originalPrice.ToString("N3");

                                // Hide discount message
                                Label discountLabel = isRightLens ? lblRightDiscount : lblLeftDiscount;
                                if (discountLabel != null) discountLabel.Visibility = Visibility.Collapsed;
                            }
                        }
                    }

                    // Select the color
                    if (lensPrescriptionColor.ColorId.HasValue)
                    {
                        // Find and select the color in the combo box
                        colorCombo.SelectedItem = colorCombo.Items.OfType<LensPrescriptionColor>()
                            .FirstOrDefault(c => c.Id == lensPrescriptionColor.ColorId.Value);
                        //foreach (LensPrescriptionColor item in colorCombo.Items)
                        //{
                        //    if (item.Id == lensPrescriptionColor.Id)
                        //    {
                        //        colorCombo.SelectedItem = item;
                        //        break;
                        //    }
                        //}

                        // Load expiration dates for this color
                        await LoadExpirationDatesForColor(lensPrescriptionColor.Id, isRightLens);

                        // Select the first expiration date if available
                        if (expCombo.Items.Count > 0)
                        {
                            expCombo.SelectedIndex = 0;
                        }
                    }

                    // If the other lens barcode field is empty, move focus there
                    if (isRightLens && txtBarCodLeft.IsVisible)
                    {
                        txtBarCodLeft.Focus();
                    }
                    else
                    {
                        // Focus on the Add button if both lenses are filled or only one is visible
                        btnAdd.Focus();
                    }
                }
                finally
                {
                    _isLoading = false;
                }
            }
            catch (Exception ex)
            {
                _isLoading = false;
                ErrorBox.Show($"حدث خطأ أثناء معالجة الباركود: {ex.Message}", "خطأ", true);
            }
        }

        // معالج الحدث المشترك لتغيير قيم SPH, CYL, POW
        private void Cmb_SPH_CYL_POW_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading || _prescriptions == null) return;

            _isLoading = true;
            try
            {
                ComboBox changedComboBox = sender as ComboBox;
                if (changedComboBox == null) return;

                // تحديد ما إذا كانت العملية للعين اليمنى أو اليسرى
                bool isRight = changedComboBox == cmbSphRight ||
                               changedComboBox == cmbCylRight ||
                               changedComboBox == cmbpowRight;

                // الحصول على القيم المحددة
                decimal? selectedSph = null;
                decimal? selectedCyl = null;
                decimal? selectedPow = null;

                ComboBox sphCombo = isRight ? cmbSphRight : cmbSphLeft;
                ComboBox cylCombo = isRight ? cmbCylRight : cmbCylLeft;
                ComboBox powCombo = isRight ? cmbpowRight : cmbpowLeft;
                ComboBox prescriptionCombo = isRight ? cmbPrescriptionRight : cmbPrescriptionLeft;

                // Get the selected values from the combo boxes (now using SelectedValue instead of SelectedItem)
                if (sphCombo.SelectedValue != null) selectedSph = (decimal)sphCombo.SelectedValue;
                if (cylCombo.SelectedValue != null) selectedCyl = (decimal)cylCombo.SelectedValue;
                if (powCombo.SelectedValue != null) selectedPow = (decimal)powCombo.SelectedValue;

                if (sphCombo.Visibility == Visibility.Visible && sphCombo.SelectedIndex == -1) return;
                if (cylCombo.Visibility == Visibility.Visible && cylCombo.SelectedIndex == -1) return;
                if (powCombo.Visibility == Visibility.Visible && powCombo.SelectedIndex == -1) return;
                // البحث عن الوصفة المطابقة
                var matchingPrescription = _prescriptions.FirstOrDefault(p =>
                    (selectedSph == null || (p.Sphere != null && p.Sphere.Value == selectedSph)) &&
                    (selectedCyl == null || (p.Cylinder != null && p.Cylinder.Value == selectedCyl)) &&
                    (selectedPow == null || (p.Pow != null && p.Pow.Value == selectedPow)));

                if (matchingPrescription != null || (int)prescriptionCombo.SelectedValue != matchingPrescription.Id)
                {
                    // تحديد الوصفة في قائمة الوصفات
                    prescriptionCombo.SelectedValue = matchingPrescription.Id;

                    // تحديث الأسعار والألوان
                    _isLoading = false;
                    if (isRight)
                    {
                        cmbPrescriptionRight_SelectionChanged(prescriptionCombo, null);
                    }
                    else
                    {
                        cmbPrescriptionLeft_SelectionChanged(prescriptionCombo, null);
                    }

                }
            }
            finally
            {
                _isLoading = false;
            }
        }
    }

}
