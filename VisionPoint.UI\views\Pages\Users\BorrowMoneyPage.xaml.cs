﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;
using static VisionPoint.UI.PL.SeedData;

namespace VisionPoint.UI.views.Pages.Users
{
    /// <summary>
    /// Interaction logic for BorrowMoneyPage.xaml
    /// </summary>
    public partial class BorrowMoneyPage : Window
    {
        private readonly ReceiptService _receiptService;
        private readonly TreasuryService _treasuryService;
        private readonly UserService _userService;
        private List<Models.Treasury> _treasuries = new List<Models.Treasury>();
        private User _currentUser;

        public BorrowMoneyPage()
        {
            InitializeComponent();
            _receiptService = new ReceiptService();
            _treasuryService = new TreasuryService();
            _userService = ServiceLocator.GetService<UserService>();
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحيات المستخدم للعمليات المالية للموظفين
            bool canPerformEmployeeFinancial = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EmployeeFinancialRole");
            if (!canPerformEmployeeFinancial)
            {
                ErrorBox.Show("لا تملك صلاحية إجراء العمليات المالية للموظفين (سلف مرتب - أخذ ورد العهدة)", "خطأ في الصلاحيات", true);
                return;
            }

            if (!decimal.TryParse(txtName.Text, out decimal borrowAmount))
            {
                DialogBox.Show("الرجاء إدخال قيمة صحيحة", "خطأ");
                return;
            }

            if (cmbTreasury.SelectedValue == null)
            {
                DialogBox.Show("الرجاء اختيار طريقة الدفع", "خطأ");
                return;
            }

            byte treasuryId = (byte)cmbTreasury.SelectedValue;


            // تحديد نوع العملية (اخذ عهدة أو رد عهدة أو سلفة راتب)
            bool isPaymentToEmployee = RdbExchange.IsChecked ?? true;
            bool isSalaryAdvance = RdbSalaryAdvance.IsChecked ?? false;

            // إذا كانت سلفة راتب، نتحقق من أن المستخدم لديه راتب وأن قيمة السلفة لا تتجاوز الراتب
            if (isSalaryAdvance)
            {
                // التحقق من أن المستخدم لديه راتب
                if (_currentUser.Salary <= 0)
                {
                    DialogBox.Show("لا يمكن سحب سلفة لأنه لا يوجد راتب مسجل للموظف", "خطأ");
                    return;
                }

                // التحقق من أن قيمة السلفة لا تتجاوز الراتب
                if (borrowAmount > _currentUser.Salary)
                {
                    DialogBox.Show($"لا يمكن سحب سلفة أكبر من قيمة الراتب ({_currentUser.Salary:N3})", "خطأ");
                    return;
                }
            }

            var receipt = new Receipt
            {
                Value = borrowAmount,
                IsExchange = isSalaryAdvance || isPaymentToEmployee, // true لاخذ عهدة أو سلفة راتب، false لرد عهدة
                Date = DateTime.Now,
                TreasuryId = treasuryId,
                EmployeeId = CurrentUser.Id,
                Statement = isSalaryAdvance ? "سلفة من الراتب" : (isPaymentToEmployee ? "اخذ عهدة" : "رد عهدة"),
                FinancialId = isSalaryAdvance ? (byte)FinancialId.SalaryPayment : (byte)FinancialId.Employee
            };

            var result = await _receiptService.CreateReceipt(receipt);
            if (result.success)
            {
                // تحديث رصيد المستخدم مباشرة في الكائن الحالي
                if (isSalaryAdvance)
                {
                    // في حالة سلفة راتب، ننقص رصيد الموظف (الموظف مدين للمتجر)
                    _currentUser.Balance -= borrowAmount;
                }
                else if (isPaymentToEmployee)
                {
                    // في حالة اخذ عهدة، نزيد رصيد الموظف (المتجر مدين للموظف)
                    _currentUser.Balance += borrowAmount;
                }
                else
                {
                    // في حالة رد عهدة، ننقص رصيد الموظف
                    _currentUser.Balance -= borrowAmount;
                }

                string successMessage;
                if (isSalaryAdvance)
                    successMessage = "تم سحب السلفة بنجاح";
                else if (isPaymentToEmployee)
                    successMessage = "تم اخذ العهدة بنجاح";
                else
                    successMessage = "تم رد العهدة بنجاح";
                DialogBox.Show(successMessage, "نجاح");
                // تفريغ حقل المبلغ
                txtName.Text = string.Empty;
                // تحديث معلومات المستخدم (للتأكد من تحديث الرصيد)
                _currentUser = await _userService.GetUserById(CurrentUser.Id);
                // تحديث عرض الرصيد
                UpdateBalanceDisplay();
                // تحديث قائمة الدفعات
                await RefreshBorrowsList();
                // تحديث طرق الدفع
                await LoadTreasuriesAsync();
            }
            else
            {
                DialogBox.Show($"حدث خطأ: {result.message}", "خطأ");
            }
        }



        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحميل معلومات المستخدم الحالي
                _currentUser = await _userService.GetUserById(CurrentUser.Id);

                // تحديث عرض رصيد المستخدم
                UpdateBalanceDisplay();

                // تحميل طرق الدفع
                await LoadTreasuriesAsync();

                // تحديث قائمة الدفعات عند تحميل النافذة
                await RefreshBorrowsList();
            }
            catch (Exception ex)
            {
                DialogBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
            }
        }

        private void UpdateBalanceDisplay()
        {
            if (_currentUser != null)
            {
                // تحديد حالة الرصيد (مدين/دائن)
                string balanceStatus;
                if (_currentUser.Balance > 0)
                {
                    balanceStatus = "مدين (الموظف مدين للمتجر)";
                    txtBalance.Foreground = new SolidColorBrush(Colors.Red);
                    txtBalanceStatus.Foreground = new SolidColorBrush(Colors.Red);
                }
                else if (_currentUser.Balance < 0)
                {
                    balanceStatus = "دائن (المتجر مدين للموظف)";
                    txtBalance.Foreground = new SolidColorBrush(Colors.Green);
                    txtBalanceStatus.Foreground = new SolidColorBrush(Colors.Green);
                }
                else
                {
                    balanceStatus = "متوازن (لا يوجد دين)";
                    txtBalance.Foreground = new SolidColorBrush(Colors.Black);
                    txtBalanceStatus.Foreground = new SolidColorBrush(Colors.Black);
                }

                // عرض الرصيد وحالته
                txtBalance.Text = $"{Math.Abs(_currentUser.Balance):N3}";
                txtBalanceStatus.Text = balanceStatus;
            }
        }

        private async Task LoadTreasuriesAsync()
        {
            try
            {
                // التحقق من وجود مخزن للمستخدم الحالي
                if (!CurrentUser.WarehouseId.HasValue)
                {
                    DialogBox.Show("لا يمكن تحميل طرق الدفع لأنه لا يوجد مخزن مرتبط بالمستخدم الحالي. يرجى التواصل مع المدير لتحديد المخزن.", "تنبيه");
                    Close();
                    return;
                }

                // تحميل طرق الدفع المرتبطة بمخزن المستخدم الحالي فقط
                _treasuries = await _treasuryService.GetTreasuriesByWarehouseAsync(CurrentUser.WarehouseId.Value);

                if (_treasuries.Count == 0)
                {
                    DialogBox.Show($"لا توجد طرق دفع متاحة في المخزن '{CurrentUser.WarehouseName}'. الرجاء إضافة طريقة دفع للمخزن أولاً.", "تنبيه");
                    Close();
                    return;
                }

                // إعداد قائمة طرق الدفع
                cmbTreasury.DisplayMemberPath = "Name";
                cmbTreasury.SelectedValuePath = "Id";
                cmbTreasury.ItemsSource = _treasuries;

                // تحديد طريقة الدفع الأولى افتراضياً
                if (_treasuries.Count > 0)
                {
                    cmbTreasury.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                DialogBox.Show($"حدث خطأ أثناء تحميل طرق الدفع: {ex.Message}", "خطأ");
                Close();
            }
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Close();
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // تعديل عرض الأعمدة ديناميكياً بناءً على عرض القائمة
            if (list.View is GridView gridView)
            {
                double remainingWidth = list.ActualWidth - SystemParameters.VerticalScrollBarWidth - 10;

                // توزيع العرض على الأعمدة بنسب متساوية تقريباً
                if (remainingWidth > 0)
                {
                    double columnWidth = remainingWidth / 4; // 4 أعمدة

                    // تعيين عرض عمود القيمة
                    gridView.Columns[0].Width = columnWidth * 0.8;

                    // تعيين عرض عمود التاريخ
                    gridView.Columns[1].Width = columnWidth * 1.0;

                    // تعيين عرض عمود البيان
                    gridView.Columns[2].Width = columnWidth * 1.0;

                    // تعيين عرض عمود طريقة الدفع
                    gridView.Columns[3].Width = columnWidth * 1.2;
                }
            }
        }


        private async Task RefreshBorrowsList()
        {
            try
            {
                // تحميل دفعات المستخدم الحالي (مرتبة من الأحدث إلى الأقدم)
                var userBorrows = await _receiptService.GetUserBorrows(CurrentUser.Id);

                // ترتيب الدفعات بحسب التاريخ (من الأحدث إلى الأقدم)
                userBorrows = userBorrows.OrderByDescending(b => b.Date).ToList();

                // تعيين مصدر البيانات للقائمة
                list.ItemsSource = userBorrows;

                // إظهار عدد العمليات في عنوان النافذة
                Title = $"عمليات الموظف {CurrentUser.Name} - {userBorrows.Count} عملية";

                // تحديث معلومات المستخدم
                _currentUser = await _userService.GetUserById(CurrentUser.Id);

                // تحديث عرض الرصيد
                UpdateBalanceDisplay();
            }
            catch (Exception ex)
            {
                DialogBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
            }
        }
    }
}
