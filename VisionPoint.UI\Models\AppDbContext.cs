﻿using Azure;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata;
using System.Runtime.CompilerServices;
using VisionPoint.UI.Properties;

namespace VisionPoint.UI.Models;

public class AppDbContext : IdentityDbContext<User, Role, int, IdentityUserClaim<int>, UserRole, IdentityUserLogin<int>, IdentityRoleClaim<int>, IdentityUserToken<int>>
{
    public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
    {
    }
    public AppDbContext()
    {

    }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // تكوين أعمدة rowguid لـ SQL Server Merge Replication
        ConfigureRowGuidColumns(modelBuilder);

        // تكوين خاص لحل مشكلة SQL Server Merge Replication triggers
        ConfigureMergeReplicationCompatibility(modelBuilder);

        // تعيين سلوك الحذف الافتراضي لجميع العلاقات إلى NoAction
        foreach (var foreignKey in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))
        {
            foreignKey.DeleteBehavior = DeleteBehavior.NoAction;
        }

        // تكوين العلاقات التي تحتاج إلى Cascade Delete

        // 1. PurchaseItem and Purchase - عند حذف فاتورة الشراء، يتم حذف عناصرها تلقائياً
        modelBuilder.Entity<PurchaseItem>(entity =>
        {
            entity.HasOne(d => d.Purchase).WithMany(p => p.PurchaseItems)
                .HasForeignKey(d => d.PurchaseId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // 2. SaleItem and Sale - عند حذف فاتورة البيع، يتم حذف عناصرها تلقائياً
        modelBuilder.Entity<SaleItem>(entity =>
        {
            entity.HasOne(d => d.Sale).WithMany(p => p.SaleItems)
                .HasForeignKey(d => d.SaleId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // 3. Product and ProductColor - عند حذف المنتج، يتم حذف ألوانه تلقائياً
        modelBuilder.Entity<ProductColor>(entity =>
        {
            entity.HasOne(d => d.Product).WithMany(p => p.ProductColors)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<ProductQuantity>(entity =>
        {
            entity.HasOne(d => d.ProductColor).WithMany(p => p.ProductQuantity)
                .HasForeignKey(d => d.ProductColorId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // 5. Lens and LensPrescription - عند حذف العدسة، يتم حذف وصفاتها تلقائياً
        modelBuilder.Entity<LensPrescription>(entity =>
        {
            entity.HasOne(d => d.Lens).WithMany(p => p.LensPrescriptions)
                .HasForeignKey(d => d.LensId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // 6. LensPrescription and LensPrescriptionColor - عند حذف وصفة العدسة، يتم حذف ألوانها تلقائياً
        modelBuilder.Entity<LensPrescriptionColor>(entity =>
        {
            entity.HasOne(d => d.LensPrescription).WithMany(p => p.LensPrescriptionColors)
                .HasForeignKey(d => d.LensPrescriptionId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<LensQuantity>(entity =>
        {
            entity.HasOne(d => d.LensPrescriptionColor).WithMany(p => p.LensQuantity)
                .HasForeignKey(d => d.LensPrescriptionColorId)
                .OnDelete(DeleteBehavior.Cascade);
        });


        // 8. Discount and related entities - عند حذف الخصم، يتم حذف علاقاته تلقائياً
        modelBuilder.Entity<DiscountProduct>(entity =>
        {
            entity.HasOne(d => d.Discount).WithMany(p => p.DiscountProducts)
                .HasForeignKey(d => d.DiscountId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<DiscountLens>(entity =>
        {
            entity.HasOne(d => d.Discount).WithMany(p => p.DiscountLenses)
                .HasForeignKey(d => d.DiscountId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<DiscountService>(entity =>
        {
            entity.HasOne(d => d.Discount).WithMany(p => p.DiscountServices)
                .HasForeignKey(d => d.DiscountId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // تكوين جدول المخازن بدون مراجع خارجية
        modelBuilder.Entity<Warehouse>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Code).IsRequired().HasMaxLength(20);
            entity.Property(e => e.CreatedAt).IsRequired();
            entity.Property(e => e.UpdatedAt).IsRequired();

            // إنشاء فهارس فريدة
            entity.HasIndex(e => e.Name).IsUnique();
            entity.HasIndex(e => e.Code).IsUnique();
        });

        // تكوين العلاقة بين Client و Warehouse
        modelBuilder.Entity<Client>()
            .HasOne(c => c.Warehouse)
            .WithMany(w => w.Clients)
            .HasForeignKey(c => c.WarehouseId)
            .OnDelete(DeleteBehavior.SetNull);

        // تكوين العلاقة بين Purchase و Warehouse
        modelBuilder.Entity<Purchase>()
            .HasOne(p => p.Warehouse)
            .WithMany(w => w.Purchases)
            .HasForeignKey(p => p.WarehouseId)
            .OnDelete(DeleteBehavior.NoAction);

        // تكوين العلاقة بين Sale و Warehouse
        modelBuilder.Entity<Sale>()
            .HasOne(s => s.Warehouse)
            .WithMany(w => w.Sales)
            .HasForeignKey(s => s.WarehouseId)
            .OnDelete(DeleteBehavior.NoAction);

        // تكوين العلاقة بين User و Warehouse
        modelBuilder.Entity<User>()
            .HasOne(u => u.Warehouse)
            .WithMany()
            .HasForeignKey(u => u.WarehouseId)
            .OnDelete(DeleteBehavior.SetNull);

        // تكوين العلاقة بين Receipt و Warehouse
        modelBuilder.Entity<Receipt>()
            .HasOne(r => r.Warehouse)
            .WithMany()
            .HasForeignKey(r => r.WarehouseId)
            .OnDelete(DeleteBehavior.SetNull);

    }

    public DbSet<Financial> Financials { get; set; }
    public DbSet<Treasury> Treasuries { get; set; }
    public DbSet<Warehouse> Warehouses { get; set; }
    public DbSet<LensCategory> LensCategories { get; set; }
    public DbSet<Lens> Lenses { get; set; }
    public DbSet<Color> Colors { get; set; }
    public DbSet<Prescription> Prescriptions { get; set; }
    public DbSet<LensPrescription> LensPrescriptions { get; set; }
    public DbSet<LensQuantity> LensQuantities { get; set; }
    public DbSet<LensPrescriptionColor> LensPrescriptionColors { get; set; }
    public DbSet<Client> Clients { get; set; }
    public DbSet<Product> Products { get; set; }
    public DbSet<ProductColor> ProductColors { get; set; }
    public DbSet<ProductQuantity> ProductQuantities { get; set; }
    public DbSet<Purchase> Purchases { get; set; }
    public DbSet<PurchaseItem> PurchaseItems { get; set; }
    public DbSet<Sale> Sales { get; set; }
    public DbSet<SaleItem> SaleItems { get; set; }
    public DbSet<Service> Services { get; set; }
    public DbSet<Receipt> Receipts { get; set; }
    public DbSet<Discount> Discounts { get; set; }
    public DbSet<DiscountProduct> DiscountProducts { get; set; }
    public DbSet<DiscountLens> DiscountLenses { get; set; }
    public DbSet<DiscountService> DiscountServices { get; set; }
    public DbSet<Expense> Expenses { get; set; }
    public DbSet<SalaryPayment> SalaryPayments { get; set; }
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            optionsBuilder.UseSqlServer(Settings.Default.ConnectionString, options =>
            {
                // تعطيل OUTPUT clause لحل مشكلة SQL Server Merge Replication triggers
                options.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
            });
        }
    }

    /// <summary>
    /// Override SaveChanges لحل مشكلة SQL Server Merge Replication triggers
    /// </summary>
    public override int SaveChanges()
    {
        try
        {
            // تجاهل تحديث أعمدة rowguid قبل الحفظ
            IgnoreRowGuidUpdates();
            return base.SaveChanges();
        }
        catch (DbUpdateException ex) when (ex.InnerException?.Message?.Contains("OUTPUT clause") == true ||
                                          ex.InnerException?.Message?.Contains("rowguidcol") == true ||
                                          ex.InnerException?.Message?.Contains("identity range check constraint") == true)
        {
            // معالجة مشكلة Identity Range في Merge Replication
            if (ex.InnerException?.Message?.Contains("identity range check constraint") == true)
            {
                HandleIdentityRangeIssue(ex);
            }

            // إعادة المحاولة بدون OUTPUT clause
            return SaveChangesWithoutOutputClause();
        }
    }

    /// <summary>
    /// Override SaveChangesAsync لحل مشكلة SQL Server Merge Replication triggers
    /// </summary>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // تجاهل تحديث أعمدة rowguid قبل الحفظ
            IgnoreRowGuidUpdates();
            return await base.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateException ex) when (ex.InnerException?.Message?.Contains("OUTPUT clause") == true ||
                                          ex.InnerException?.Message?.Contains("rowguidcol") == true ||
                                          ex.InnerException?.Message?.Contains("identity range check constraint") == true)
        {
            // معالجة مشكلة Identity Range في Merge Replication
            if (ex.InnerException?.Message?.Contains("identity range check constraint") == true)
            {
                await HandleIdentityRangeIssueAsync(ex, cancellationToken);
            }

            // إعادة المحاولة بدون OUTPUT clause
            return await SaveChangesWithoutOutputClauseAsync(cancellationToken);
        }
    }

    /// <summary>
    /// معالجة مشكلة Identity Range في SQL Server Merge Replication
    /// </summary>
    private void HandleIdentityRangeIssue(DbUpdateException ex)
    {
        try
        {
            // استخراج اسم الجدول من رسالة الخطأ
            var tableName = ExtractTableNameFromIdentityRangeError(ex.InnerException?.Message);

            if (!string.IsNullOrEmpty(tableName))
            {
                // تنفيذ sp_adjustpublisheridentityrange لإعادة تعيين نطاق Identity
                var sql = $"EXEC sp_adjustpublisheridentityrange @table_name = '{tableName}'";
                Database.ExecuteSqlRaw(sql);

                // انتظار قصير للسماح بتحديث النطاق
                System.Threading.Thread.Sleep(1000);
            }
        }
        catch (Exception adjustEx)
        {
            // تسجيل الخطأ ولكن لا نرمي استثناء جديد
            System.Diagnostics.Debug.WriteLine($"فشل في تعديل نطاق Identity: {adjustEx.Message}");
        }
    }

    /// <summary>
    /// معالجة مشكلة Identity Range في SQL Server Merge Replication (async)
    /// </summary>
    private async Task HandleIdentityRangeIssueAsync(DbUpdateException ex, CancellationToken cancellationToken)
    {
        try
        {
            // استخراج اسم الجدول من رسالة الخطأ
            var tableName = ExtractTableNameFromIdentityRangeError(ex.InnerException?.Message);

            if (!string.IsNullOrEmpty(tableName))
            {
                // تنفيذ sp_adjustpublisheridentityrange لإعادة تعيين نطاق Identity
                var sql = $"EXEC sp_adjustpublisheridentityrange @table_name = '{tableName}'";
                await Database.ExecuteSqlRawAsync(sql, cancellationToken);

                // انتظار قصير للسماح بتحديث النطاق
                await Task.Delay(1000, cancellationToken);
            }
        }
        catch (Exception adjustEx)
        {
            // تسجيل الخطأ ولكن لا نرمي استثناء جديد
            System.Diagnostics.Debug.WriteLine($"فشل في تعديل نطاق Identity: {adjustEx.Message}");
        }
    }

    /// <summary>
    /// استخراج اسم الجدول من رسالة خطأ Identity Range
    /// </summary>
    private string ExtractTableNameFromIdentityRangeError(string errorMessage)
    {
        try
        {
            if (string.IsNullOrEmpty(errorMessage))
                return null;

            // البحث عن نمط "replicated table 'dbo.TableName'"
            var pattern = @"replicated table 'dbo\.(\w+)'";
            var match = System.Text.RegularExpressions.Regex.Match(errorMessage, pattern);

            if (match.Success && match.Groups.Count > 1)
            {
                return match.Groups[1].Value;
            }

            // إذا لم نجد النمط، نحاول البحث عن أي اسم جدول
            var tablePattern = @"table '.*?\.(\w+)'";
            var tableMatch = System.Text.RegularExpressions.Regex.Match(errorMessage, tablePattern);

            if (tableMatch.Success && tableMatch.Groups.Count > 1)
            {
                return tableMatch.Groups[1].Value;
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// حفظ التغييرات بدون استخدام OUTPUT clause
    /// </summary>
    private int SaveChangesWithoutOutputClause()
    {
        var entries = ChangeTracker.Entries().Where(e => e.State != EntityState.Unchanged).ToList();
        int affectedRows = 0;

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    affectedRows += SaveAddedEntity(entry);
                    break;
                case EntityState.Modified:
                    affectedRows += SaveModifiedEntity(entry);
                    break;
                case EntityState.Deleted:
                    affectedRows += SaveDeletedEntity(entry);
                    break;
            }
        }

        return affectedRows;
    }

    /// <summary>
    /// حفظ التغييرات بدون استخدام OUTPUT clause (async)
    /// </summary>
    private async Task<int> SaveChangesWithoutOutputClauseAsync(CancellationToken cancellationToken)
    {
        var entries = ChangeTracker.Entries().Where(e => e.State != EntityState.Unchanged).ToList();
        int affectedRows = 0;

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    affectedRows += await SaveAddedEntityAsync(entry, cancellationToken);
                    break;
                case EntityState.Modified:
                    affectedRows += await SaveModifiedEntityAsync(entry, cancellationToken);
                    break;
                case EntityState.Deleted:
                    affectedRows += await SaveDeletedEntityAsync(entry, cancellationToken);
                    break;
            }
        }

        return affectedRows;
    }

    /// <summary>
    /// حفظ كيان مضاف بدون OUTPUT clause
    /// </summary>
    private int SaveAddedEntity(EntityEntry entry)
    {
        try
        {
            var tableName = entry.Metadata.GetTableName();
            var properties = entry.Properties.Where(p => !p.Metadata.IsKey() && p.CurrentValue != null && p.Metadata.Name != "RowGuid").ToList();

            var columnNames = string.Join(", ", properties.Select(p => $"[{p.Metadata.GetColumnName()}]"));
            var parameterNames = string.Join(", ", properties.Select((p, i) => $"@p{i}"));

            var sql = $"INSERT INTO [{tableName}] ({columnNames}) VALUES ({parameterNames})";

            var parameters = properties.Select((p, i) => new SqlParameter($"@p{i}", p.CurrentValue ?? DBNull.Value)).ToArray();

            var result = Database.ExecuteSqlRaw(sql, parameters);
            entry.State = EntityState.Unchanged;
            return result;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// حفظ كيان مضاف بدون OUTPUT clause (async)
    /// </summary>
    private async Task<int> SaveAddedEntityAsync(EntityEntry entry, CancellationToken cancellationToken)
    {
        try
        {
            var tableName = entry.Metadata.GetTableName();
            var properties = entry.Properties.Where(p => !p.Metadata.IsKey() && p.CurrentValue != null && p.Metadata.Name != "RowGuid").ToList();

            var columnNames = string.Join(", ", properties.Select(p => $"[{p.Metadata.GetColumnName()}]"));
            var parameterNames = string.Join(", ", properties.Select((p, i) => $"@p{i}"));

            var sql = $"INSERT INTO [{tableName}] ({columnNames}) VALUES ({parameterNames})";

            var parameters = properties.Select((p, i) => new SqlParameter($"@p{i}", p.CurrentValue ?? DBNull.Value)).ToArray();

            var result = await Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
            entry.State = EntityState.Unchanged;
            return result;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// حفظ كيان محدث بدون OUTPUT clause
    /// </summary>
    private int SaveModifiedEntity(EntityEntry entry)
    {
        try
        {
            var tableName = entry.Metadata.GetTableName();
            var modifiedProperties = entry.Properties.Where(p => p.IsModified && !p.Metadata.IsKey() && p.Metadata.Name != "RowGuid").ToList();
            var keyProperties = entry.Properties.Where(p => p.Metadata.IsKey()).ToList();

            if (!modifiedProperties.Any()) return 0;

            var setClause = string.Join(", ", modifiedProperties.Select((p, i) => $"[{p.Metadata.GetColumnName()}] = @p{i}"));
            var whereClause = string.Join(" AND ", keyProperties.Select((p, i) => $"[{p.Metadata.GetColumnName()}] = @k{i}"));

            var sql = $"UPDATE [{tableName}] SET {setClause} WHERE {whereClause}";

            var parameters = modifiedProperties.Select((p, i) => new SqlParameter($"@p{i}", p.CurrentValue ?? DBNull.Value))
                .Concat(keyProperties.Select((p, i) => new SqlParameter($"@k{i}", p.CurrentValue ?? DBNull.Value)))
                .ToArray();

            var result = Database.ExecuteSqlRaw(sql, parameters);
            entry.State = EntityState.Unchanged;
            return result;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// حفظ كيان محدث بدون OUTPUT clause (async)
    /// </summary>
    private async Task<int> SaveModifiedEntityAsync(EntityEntry entry, CancellationToken cancellationToken)
    {
        try
        {
            var tableName = entry.Metadata.GetTableName();
            var modifiedProperties = entry.Properties.Where(p => p.IsModified && !p.Metadata.IsKey() && p.Metadata.Name != "RowGuid").ToList();
            var keyProperties = entry.Properties.Where(p => p.Metadata.IsKey()).ToList();

            if (!modifiedProperties.Any()) return 0;

            var setClause = string.Join(", ", modifiedProperties.Select((p, i) => $"[{p.Metadata.GetColumnName()}] = @p{i}"));
            var whereClause = string.Join(" AND ", keyProperties.Select((p, i) => $"[{p.Metadata.GetColumnName()}] = @k{i}"));

            var sql = $"UPDATE [{tableName}] SET {setClause} WHERE {whereClause}";

            var parameters = modifiedProperties.Select((p, i) => new SqlParameter($"@p{i}", p.CurrentValue ?? DBNull.Value))
                .Concat(keyProperties.Select((p, i) => new SqlParameter($"@k{i}", p.CurrentValue ?? DBNull.Value)))
                .ToArray();

            var result = await Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
            entry.State = EntityState.Unchanged;
            return result;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// حفظ كيان محذوف بدون OUTPUT clause
    /// </summary>
    private int SaveDeletedEntity(EntityEntry entry)
    {
        try
        {
            var tableName = entry.Metadata.GetTableName();
            var keyProperties = entry.Properties.Where(p => p.Metadata.IsKey()).ToList();

            var whereClause = string.Join(" AND ", keyProperties.Select((p, i) => $"[{p.Metadata.GetColumnName()}] = @k{i}"));
            var sql = $"DELETE FROM [{tableName}] WHERE {whereClause}";

            var parameters = keyProperties.Select((p, i) => new SqlParameter($"@k{i}", p.OriginalValue ?? DBNull.Value)).ToArray();

            var result = Database.ExecuteSqlRaw(sql, parameters);
            entry.State = EntityState.Detached;
            return result;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// حفظ كيان محذوف بدون OUTPUT clause (async)
    /// </summary>
    private async Task<int> SaveDeletedEntityAsync(EntityEntry entry, CancellationToken cancellationToken)
    {
        try
        {
            var tableName = entry.Metadata.GetTableName();
            var keyProperties = entry.Properties.Where(p => p.Metadata.IsKey()).ToList();

            var whereClause = string.Join(" AND ", keyProperties.Select((p, i) => $"[{p.Metadata.GetColumnName()}] = @k{i}"));
            var sql = $"DELETE FROM [{tableName}] WHERE {whereClause}";

            var parameters = keyProperties.Select((p, i) => new SqlParameter($"@k{i}", p.OriginalValue ?? DBNull.Value)).ToArray();

            var result = await Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
            entry.State = EntityState.Detached;
            return result;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// تجاهل تحديث أعمدة rowguid لتجنب خطأ "Updating columns with the rowguidcol property is not allowed"
    /// </summary>
    private void IgnoreRowGuidUpdates()
    {
        foreach (var entry in ChangeTracker.Entries())
        {
            if (entry.State == EntityState.Modified)
            {
                // البحث عن خاصية RowGuid وتجاهل تحديثها
                var rowGuidProperty = entry.Properties.FirstOrDefault(p => p.Metadata.Name == "RowGuid");
                if (rowGuidProperty != null)
                {
                    rowGuidProperty.IsModified = false;
                }
            }
        }
    }

    /// <summary>
    /// تكوين أعمدة rowguid لـ SQL Server Merge Replication
    /// </summary>
    private void ConfigureRowGuidColumns(ModelBuilder modelBuilder)
    {
        // تكوين أعمدة rowguid لجميع الكيانات
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var rowGuidProperty = entityType.FindProperty("RowGuid");
            if (rowGuidProperty != null)
            {
                modelBuilder.Entity(entityType.ClrType)
                    .Property("RowGuid")
                    .HasColumnName("rowguid")
                    .HasDefaultValueSql("newsequentialid()")
                    .ValueGeneratedOnAdd()
                    .IsRequired()
                    .Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore); // تجاهل التحديث بعد الحفظ
            }
        }
    }

    /// <summary>
    /// تكوين خاص لحل مشكلة SQL Server Merge Replication triggers مع OUTPUT clause
    /// </summary>
    private void ConfigureMergeReplicationCompatibility(ModelBuilder modelBuilder)
    {
        // تكوين جداول Identity لتجنب مشكلة OUTPUT clause مع triggers
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            // تعطيل استخدام OUTPUT clause للجداول التي تحتوي على triggers
            modelBuilder.Entity(entityType.ClrType)
                .ToTable(tb => tb.HasTrigger($"MSmerge_ins_{entityType.GetTableName()}"))
                .ToTable(tb => tb.HasTrigger($"MSmerge_upd_{entityType.GetTableName()}"))
                .ToTable(tb => tb.HasTrigger($"MSmerge_del_{entityType.GetTableName()}"));
        }

        // تكوين خاص لجداول ASP.NET Identity
        modelBuilder.Entity<User>()
            .ToTable("AspNetUsers", tb =>
            {
                tb.HasTrigger("MSmerge_ins_AspNetUsers");
                tb.HasTrigger("MSmerge_upd_AspNetUsers");
                tb.HasTrigger("MSmerge_del_AspNetUsers");
            });

        modelBuilder.Entity<Role>()
            .ToTable("AspNetRoles", tb =>
            {
                tb.HasTrigger("MSmerge_ins_AspNetRoles");
                tb.HasTrigger("MSmerge_upd_AspNetRoles");
                tb.HasTrigger("MSmerge_del_AspNetRoles");
            });

        modelBuilder.Entity<UserRole>()
            .ToTable("AspNetUserRoles", tb =>
            {
                tb.HasTrigger("MSmerge_ins_AspNetUserRoles");
                tb.HasTrigger("MSmerge_upd_AspNetUserRoles");
                tb.HasTrigger("MSmerge_del_AspNetUserRoles");
            });

        // تكوين عمود RowGuid لجدول AspNetUserRoles
        modelBuilder.Entity<UserRole>()
            .Property(ur => ur.RowGuid)
            .HasColumnName("rowguid")
            .HasDefaultValueSql("newsequentialid()")
            .ValueGeneratedOnAdd()
            .IsRequired()
            .Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore); // تجاهل التحديث بعد الحفظ
    }

    ///// <summary>
    ///// إعادة تعيين نطاقات Identity لجميع الجداول في Merge Replication
    ///// يجب استدعاء هذه الدالة عند حدوث مشاكل في نطاقات Identity
    ///// </summary>
    //public async Task<bool> ResetAllIdentityRangesAsync()
    //{
    //    try
    //    {
    //        // قائمة بجميع الجداول التي تحتوي على أعمدة Identity
    //        var tablesWithIdentity = new[]
    //        {
    //            "Clients", "Products", "Warehouses", "Treasuries", "Sales", "SaleItems",
    //            "Purchases", "PurchaseItems", "Receipts", "Expenses", "Financials",
    //            "Prescriptions", "LensPrescriptions", "LensQuantities", "LensPrescriptionColors",
    //            "ProductColors", "ProductQuantities", "Lens", "LensCategories", "Colors",
    //            "Services", "Discounts", "DiscountProducts", "DiscountLenses", "DiscountServices",
    //            "SalaryPayments"
    //        };

    //        foreach (var tableName in tablesWithIdentity)
    //        {
    //            try
    //            {
    //                var sql = $"EXEC sp_adjustpublisheridentityrange @table_name = '{tableName}'";
    //                await Database.ExecuteSqlRawAsync(sql);

    //                // انتظار قصير بين كل جدول
    //                await Task.Delay(500);
    //            }
    //            catch (Exception ex)
    //            {
    //                System.Diagnostics.Debug.WriteLine($"فشل في إعادة تعيين نطاق Identity للجدول {tableName}: {ex.Message}");
    //            }
    //        }

    //        return true;
    //    }
    //    catch (Exception ex)
    //    {
    //        System.Diagnostics.Debug.WriteLine($"فشل في إعادة تعيين نطاقات Identity: {ex.Message}");
    //        return false;
    //    }
    //}

    ///// <summary>
    ///// التحقق من حالة نطاقات Identity لجميع الجداول
    ///// </summary>
    //public async Task<Dictionary<string, (long CurrentValue, long MaxValue, bool IsNearLimit)>> CheckIdentityRangesAsync()
    //{
    //    var result = new Dictionary<string, (long CurrentValue, long MaxValue, bool IsNearLimit)>();

    //    try
    //    {
    //        var sql = @"
    //            SELECT
    //                t.name AS TableName,
    //                IDENT_CURRENT(t.name) AS CurrentValue,
    //                c.max_value AS MaxValue,
    //                CASE
    //                    WHEN IDENT_CURRENT(t.name) > (c.max_value * 0.9) THEN 1
    //                    ELSE 0
    //                END AS IsNearLimit
    //            FROM sys.tables t
    //            INNER JOIN sys.columns c ON t.object_id = c.object_id
    //            WHERE c.is_identity = 1
    //            AND t.name IN (
    //                'Clients', 'Products', 'Warehouses', 'Treasuries', 'Sales', 'SaleItems',
    //                'Purchases', 'PurchaseItems', 'Receipts', 'Expenses', 'Financials',
    //                'Prescriptions', 'LensPrescriptions', 'LensQuantities', 'LensPrescriptionColors',
    //                'ProductColors', 'ProductQuantities', 'Lens', 'LensCategories', 'Colors',
    //                'Services', 'Discounts', 'DiscountProducts', 'DiscountLenses', 'DiscountServices',
    //                'SalaryPayments'
    //            )";

    //        using var command = Database.GetDbConnection().CreateCommand();
    //        command.CommandText = sql;

    //        if (Database.GetDbConnection().State != System.Data.ConnectionState.Open)
    //            await Database.GetDbConnection().OpenAsync();

    //        using var reader = await command.ExecuteReaderAsync();
    //        while (await reader.ReadAsync())
    //        {
    //            var tableName = reader["TableName"].ToString();
    //            var currentValue = Convert.ToInt64(reader["CurrentValue"]);
    //            var maxValue = Convert.ToInt64(reader["MaxValue"]);
    //            var isNearLimit = Convert.ToBoolean(reader["IsNearLimit"]);

    //            result[tableName] = (currentValue, maxValue, isNearLimit);
    //        }
    //    }
    //    catch (Exception ex)
    //    {
    //        System.Diagnostics.Debug.WriteLine($"فشل في التحقق من نطاقات Identity: {ex.Message}");
    //    }

    //    return result;
    //}
}
public static class DBEx
{
    public static async Task<(bool State, string Message)> SaveWithTransaction(this AppDbContext _db)
    {
        using (var transaction = _db.Database.BeginTransaction())
        {
            try
            {
                await _db.SaveChangesAsync();
                transaction.Commit();
                return (true, "تم الحفظ");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                Reverse(_db);
                return (false, HandleException(ex));
            }
        }
    }

    public static void Reverse(this AppDbContext _db)
    {
        var changedEntries = _db.ChangeTracker.Entries().Where(x => x.State != EntityState.Unchanged).ToList(); // get all the changed entries
        foreach (var entry in changedEntries)
        {
            switch (entry.State)
            {
                case EntityState.Modified:
                    entry.CurrentValues.SetValues(entry.OriginalValues); // reset the current values to the original values
                    entry.State = EntityState.Unchanged; // mark the entry as unchanged
                    break;
                case EntityState.Added:
                    entry.State = EntityState.Detached; // detach the entry from the context
                    break;
                case EntityState.Deleted:
                    entry.State = EntityState.Unchanged; // mark the entry as unchanged
                    break;
            }
        }
    }

    private static string HandleException(Exception exception)
    {
        if (exception is DbUpdateException dbUpdateEx)
        {
            if (dbUpdateEx.InnerException != null)
            {
                if (dbUpdateEx.InnerException is SqlException sqlException)
                {
                    switch (sqlException.Number)
                    {
                        case 547: // Foreign key constraint violation
                            // استخراج اسم الجدول من رسالة الخطأ
                            string errorMessage = sqlException.Message;
                            string tableName = GetTableNameFromErrorMessage(errorMessage);
                            return $"لا يمكن الحذف لأن هذه البيانات مستخدمة في {GetArabicTableName(tableName)}";
                        case 2601: // Unique constraint violation
                            return "هذه البيانات موجودة مسبقا";
                        case 2627: // Unique constraint violation
                            return "هذه البيانات موجودة مسبقا (مفتاح مكرر)";
                        case 8152: // String data truncation
                            return "البيانات المدخلة أكبر من الحجم المسموح به";
                        default:
                            return $"خطأ في الاتصال بقاعدة البيانات: {sqlException.Number}";
                    }
                }
            }
        }
        return exception.Message;
    }

    // دالة لاستخراج اسم الجدول من رسالة الخطأ
    private static string GetTableNameFromErrorMessage(string errorMessage)
    {
        try
        {
            // استخراج اسم الجدول من رسالة الخطأ SQL Server
            // مثال: "The DELETE statement conflicted with the REFERENCE constraint "FK_TableName_OtherTable". The conflict occurred in database "DB", table "dbo.TableName", column 'Id'."
            if (errorMessage.Contains("table"))
            {
                int tableIndex = errorMessage.IndexOf("table");
                if (tableIndex > 0)
                {
                    string tableSubstring = errorMessage.Substring(tableIndex);
                    int startQuote = tableSubstring.IndexOf("\"");
                    if (startQuote > 0)
                    {
                        int endQuote = tableSubstring.IndexOf("\"", startQuote + 1);
                        if (endQuote > startQuote)
                        {
                            string fullTableName = tableSubstring.Substring(startQuote + 1, endQuote - startQuote - 1);
                            // استخراج اسم الجدول بدون dbo.
                            if (fullTableName.Contains("."))
                            {
                                return fullTableName.Substring(fullTableName.IndexOf(".") + 1);
                            }
                            return fullTableName;
                        }
                    }
                }
            }

            // إذا لم نتمكن من استخراج اسم الجدول، نعيد رسالة عامة
            return "جدول آخر";
        }
        catch
        {
            return "جدول آخر";
        }
    }

    // دالة لتحويل اسم الجدول الإنجليزي إلى اسم عربي
    private static string GetArabicTableName(string tableName)
    {
        switch (tableName.ToLower())
        {
            case "clients": return "العملاء";
            case "colors": return "الألوان";
            case "discounts": return "الخصومات";
            case "discountlenses": return "خصومات العدسات";
            case "discountproducts": return "خصومات المنتجات";
            case "discountservices": return "خصومات الخدمات";
            case "expenses": return "المصروفات";
            case "financials": return "الحسابات المالية";
            case "lens": return "العدسات";
            case "lenscategories": return "أنواع العدسات";
            case "lensprescriptions": return "وصفات العدسات";
            case "lensprescriptioncolors": return "ألوان وصفات العدسات";
            case "lensquantities": return "كميات العدسات";
            case "prescriptions": return "الوصفات الطبية";
            case "products": return "المنتجات";
            case "productcolors": return "ألوان المنتجات";
            case "productquantities": return "كميات المنتجات";
            case "purchases": return "المشتريات";
            case "purchaseitems": return "عناصر المشتريات";
            case "receipts": return "الإيصالات";
            case "sales": return "المبيعات";
            case "saleitems": return "عناصر المبيعات";
            case "services": return "الخدمات";
            case "treasuries": return "الخزائن";
            case "warehouses": return "المخازن";
            case "users": return "المستخدمين";
            case "roles": return "الأدوار";
            case "salarypayments": return "دفعات المرتبات";
            default: return tableName;
        }
    }
}
