# دليل استيراد العدسات - VisionPoint

## نظرة عامة
تم إصلاح وتحسين نظام استيراد العدسات من ملفات Excel لحل المشاكل التقنية التالية:
- تكرار الألوان
- أخطاء المفاتيح المكررة
- فشل حفظ البيانات
- عدم حفظ الكميات

## المكونات الرئيسية

### 1. ExcelLensService
**المسار:** `VisionPoint.UI/PL/ExcelLensService.cs`

**الوظائف الرئيسية:**
- `ImportExcel()`: الدالة الرئيسية للاستيراد
- `GetOrCreateColor()`: إدارة الألوان بدون تكرار
- `UpdateQuantities()`: حفظ الكميات مع ربط صحيح
- `ValidateDataIntegrity()`: التحقق من تكامل البيانات

### 2. LensImportErrorHandler
**المسار:** `VisionPoint.UI/Helper/ImportExport/LensImportErrorHandler.cs`

**الوظائف:**
- `CategorizeErrors()`: تصنيف الأخطاء حسب النوع
- `GenerateErrorReport()`: إنشاء تقارير مفصلة
- `SuggestSolutions()`: اقتراح حلول للأخطاء

### 3. LenssesImporterPage
**المسار:** `VisionPoint.UI/views/Pages/ImportExport/LenssesImporterPage.xaml.cs`

**التحسينات:**
- عرض أخطاء مصنفة
- تصدير تقارير نصية
- اقتراحات تلقائية للحلول

## الحلول المطبقة

### 1. إصلاح تكرار الألوان
```csharp
// تحسين تحميل الألوان الموجودة
var colorsDict = new Dictionary<string, Color>();
foreach (var color in colorsList)
{
    // إضافة بالكود السادس عشري
    if (!string.IsNullOrEmpty(color.HexCode) && !colorsDict.ContainsKey(color.HexCode))
        colorsDict[color.HexCode] = color;
    
    // إضافة بالاسم
    if (!string.IsNullOrEmpty(color.Name) && !colorsDict.ContainsKey(color.Name))
        colorsDict[color.Name] = color;
}
```

### 2. إصلاح حفظ الكميات
```csharp
// ربط صحيح للكميات
var newQuantity = new LensQuantity
{
    LensPrescriptionColor = color,
    LensPrescriptionColorId = color.Id,  // المفتاح الخارجي
    Exp = exp,
    Quantity = qte,
    WarehouseId = _warehouseId.Value
};

// إضافة للـ Context مباشرة
_context.LensQuantities.AddWithBusy(newQuantity);
```

### 3. إزالة الحفظ الفوري
```csharp
// قبل الإصلاح (مشكلة)
_context.Colors.AddWithBusy(newColor);
_context.SaveChangesWithBusy("SaveNewColor");  // ❌ حفظ فوري

// بعد الإصلاح (حل)
_context.Colors.AddWithBusy(newColor);
// الحفظ في نهاية العملية فقط ✅
```

### 4. التحقق من تكامل البيانات
```csharp
// فحص الألوان المكررة
var duplicateColors = addedColors
    .GroupBy(c => new { c.Name, c.HexCode })
    .Where(g => g.Count() > 1)
    .ToList();

// فحص الكميات غير المرتبطة
var invalidQuantities = addedQuantities
    .Where(q => q.LensPrescriptionColorId == null || q.LensPrescriptionColorId == 0)
    .ToList();
```

## كيفية الاستخدام

### 1. استيراد عادي
```csharp
var excelService = new ExcelLensService();
excelService.SetWarehouseId(warehouseId);
excelService.SetColumnMapping(columnMapping);

var (added, updated, errors, errorRecords) = await excelService.ImportExcel(filePath, progress);
```

### 2. معالجة الأخطاء
```csharp
if (errors.Any())
{
    var categorizedErrors = LensImportErrorHandler.CategorizeErrors(errors);
    var suggestions = LensImportErrorHandler.SuggestSolutions(errors);
    var report = LensImportErrorHandler.GenerateErrorReport(errors, errorRecords);
}
```

## تنسيق ملف Excel المطلوب

### الأعمدة الإجبارية:
- **اسم العدسة**: اسم فريد للعدسة
- **نوع العدسة**: فئة العدسة
- **الكمية**: عدد صحيح
- **سعر التكلفة**: رقم عشري
- **سعر البيع**: رقم عشري

### الأعمدة الاختيارية:
- **باركود**: كود فريد للمنتج
- **كود اللون**: كود سادس عشري (#FF0000)
- **اسم اللون**: اسم اللون
- **SPH, CYL, POW**: قيم المقاسات
- **BC, DIA, ADD, AXIS**: خصائص إضافية

## نصائح للاستخدام الأمثل

### 1. إعداد البيانات
- تأكد من إضافة جميع المقاسات المطلوبة قبل الاستيراد
- راجع أسماء العدسات للتأكد من عدم التكرار
- استخدم أكواد ألوان صحيحة (#RRGGBB)

### 2. معالجة الأخطاء
- راجع التقرير المفصل للأخطاء
- اتبع الاقتراحات المقدمة
- صحح البيانات وأعد المحاولة

### 3. مراقبة الأداء
- استخدم ملفات صغيرة للاختبار أولاً
- راقب استخدام الذاكرة مع الملفات الكبيرة
- تأكد من وجود مساحة كافية في قاعدة البيانات

## الاختبارات

### تشغيل الاختبارات:
```bash
dotnet test VisionPoint.UI.Tests/LensImportTests.cs
```

### اختبارات متاحة:
- `TestErrorCategorization`: اختبار تصنيف الأخطاء
- `TestErrorReportGeneration`: اختبار إنشاء التقارير
- `TestSolutionSuggestions`: اختبار اقتراح الحلول

## استكشاف الأخطاء

### مشاكل شائعة:
1. **"مقاس غير موجود"**: أضف المقاس في جدول المقاسات
2. **"لون مكرر"**: راجع أكواد وأسماء الألوان
3. **"كمية غير مرتبطة"**: تأكد من تحديد المخزن
4. **"فشل الحفظ"**: راجع سجل الأخطاء التفصيلي

### ملفات السجل:
- أخطاء الاستيراد: `LensImportErrors_[التاريخ].xlsx`
- التقرير النصي: `LensImportErrors_[التاريخ]_Report.txt`
