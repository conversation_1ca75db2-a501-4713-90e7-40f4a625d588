using System.Text;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using static VisionPoint.UI.PL.ExcelLensService;

namespace VisionPoint.UI.Helper.ImportExport;

/// <summary>
/// معالج أخطاء استيراد العدسات
/// </summary>
public static class LensImportErrorHandler
{
    /// <summary>
    /// تصنيف الأخطاء حسب النوع
    /// </summary>
    public static Dictionary<string, List<string>> CategorizeErrors(List<string> errors)
    {
        var categorizedErrors = new Dictionary<string, List<string>>
        {
            ["أخطاء البيانات"] = new List<string>(),
            ["أخطاء الألوان"] = new List<string>(),
            ["أخطاء الكميات"] = new List<string>(),
            ["أخطاء الفئات"] = new List<string>(),
            ["أخطاء عامة"] = new List<string>()
        };

        foreach (var error in errors)
        {
            if (error.Contains("لون") || error.Contains("Color"))
            {
                categorizedErrors["أخطاء الألوان"].Add(error);
            }
            else if (error.Contains("كمية") || error.Contains("Quantity"))
            {
                categorizedErrors["أخطاء الكميات"].Add(error);
            }
            else if (error.Contains("فئة") || error.Contains("Category"))
            {
                categorizedErrors["أخطاء الفئات"].Add(error);
            }
            else if (error.Contains("SPH") || error.Contains("CYL") || error.Contains("POW"))
            {
                categorizedErrors["أخطاء البيانات"].Add(error);
            }
            else
            {
                categorizedErrors["أخطاء عامة"].Add(error);
            }
        }

        // إزالة الفئات الفارغة
        return categorizedErrors.Where(kvp => kvp.Value.Any()).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// إنشاء تقرير مفصل للأخطاء
    /// </summary>
    public static string GenerateErrorReport(List<string> errors, List<ExcelRecord> errorRecords)
    {
        var report = new StringBuilder();
        report.AppendLine("=== تقرير أخطاء استيراد العدسات ===");
        report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine($"إجمالي الأخطاء: {errors.Count}");
        report.AppendLine($"السجلات المرفوضة: {errorRecords.Count}");
        report.AppendLine();

        // تصنيف الأخطاء
        var categorizedErrors = CategorizeErrors(errors);
        
        foreach (var category in categorizedErrors)
        {
            report.AppendLine($"=== {category.Key} ({category.Value.Count}) ===");
            foreach (var error in category.Value)
            {
                report.AppendLine($"- {error}");
            }
            report.AppendLine();
        }

        // تفاصيل السجلات المرفوضة
        if (errorRecords.Any())
        {
            report.AppendLine("=== تفاصيل السجلات المرفوضة ===");
            foreach (var record in errorRecords.Take(10)) // أول 10 سجلات فقط
            {
                report.AppendLine($"العدسة: {record.Name}");
                report.AppendLine($"الخطأ: {record.Error}");
                report.AppendLine($"البيانات: SPH={record.SPH}, CYL={record.CYL}, POW={record.Pow}");
                report.AppendLine("---");
            }
            
            if (errorRecords.Count > 10)
            {
                report.AppendLine($"... و {errorRecords.Count - 10} سجل آخر");
            }
        }

        return report.ToString();
    }

    /// <summary>
    /// اقتراح حلول للأخطاء الشائعة
    /// </summary>
    public static List<string> SuggestSolutions(List<string> errors)
    {
        var suggestions = new List<string>();

        if (errors.Any(e => e.Contains("مكرر")))
        {
            suggestions.Add("تأكد من عدم وجود بيانات مكررة في ملف Excel");
            suggestions.Add("راجع أسماء العدسات والألوان للتأكد من عدم التكرار");
        }

        if (errors.Any(e => e.Contains("غير موجود") && e.Contains("مقاس")))
        {
            suggestions.Add("تأكد من إضافة جميع المقاسات المطلوبة في جدول المقاسات أولاً");
            suggestions.Add("راجع قيم SPH, CYL, POW في ملف Excel");
        }

        if (errors.Any(e => e.Contains("كمية")))
        {
            suggestions.Add("تأكد من تحديد المخزن الصحيح قبل الاستيراد");
            suggestions.Add("راجع صيغة الكميات في ملف Excel (يجب أن تكون أرقام صحيحة)");
        }

        if (errors.Any(e => e.Contains("لون")))
        {
            suggestions.Add("تأكد من صحة أكواد الألوان السادسة عشرية");
            suggestions.Add("راجع أسماء الألوان للتأكد من عدم وجود أحرف خاصة");
        }

        return suggestions;
    }
}
