﻿using ClosedXML.Excel; // مكتبة لإنشاء وتعديل ملفات Excel
using ExcelDataReader;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.IO;
using System.Text;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL;

public class ExcelLensService : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;
    // تخطيط الأعمدة المخصص
    private Dictionary<string, string> _customColumnMapping;
    // معرف المخزن المحدد للاستيراد
    private int? _warehouseId;

    public ExcelLensService()
    {
    }

    // تعيين معرف المخزن للاستيراد
    public void SetWarehouseId(int warehouseId)
    {
        _warehouseId = warehouseId;
    }

    // تعيين تخطيط الأعمدة المخصص
    public void SetColumnMapping(Dictionary<string, string> columnMapping)
    {
        _customColumnMapping = columnMapping;
    }

    public async Task<(int Added, int Updated, List<string> Errors, List<ExcelRecord> errorRecords)> ImportExcel(string filePath, IProgress<ProgressReport> progress = null)
    {
        var errors = new List<string>();
        var errorRecords = new List<ExcelRecord>(); // لتجميع السجلات التي بها أخطاء
        int added = 0, updated = 0;
        var state = new ProgressState();

        // التحقق من وجود مخزن محدد
        if (!_warehouseId.HasValue)
        {
            errors.Add("لم يتم تحديد مخزن للاستيراد");
            return (0, 0, errors, errorRecords);
        }

        try
        {
            progress?.Report(new ProgressReport { Percentage = 5, Message = "جاري قراءة ملف Excel..." });
            var records = await ReadExcelData(filePath);

            if (records.Count == 0)
            {
                errors.Add("لم يتم العثور على أي سجلات صالحة في الملف");
                return (0, 0, errors, errorRecords);
            }

            state.Total = records.Count;

            progress?.Report(new ProgressReport { Percentage = 10, Message = "جاري تحليل البيانات..." });
            var recordGroups = records.GroupBy(r => r.Name).ToList();
            var existingData = await GetExistingData(records);

            progress?.Report(new ProgressReport { Percentage = 20, Message = "جاري معالجة البيانات..." });
            foreach (var group in recordGroups)
            {
                var lensName = group.Key;
                var existingLens = existingData.Lenses.GetValueOrDefault(lensName);

                // في حال وجود العدسة مع اختلاف الخصائص
                var firstRecord = group.First();
                if (existingLens != null && (existingLens.Dia != firstRecord.Dia || existingLens.BC != firstRecord.BC ||
                                              existingLens.Addtion != firstRecord.Add || existingLens.Axis != firstRecord.Axis))
                {
                    string groupError = $"خطأ: العدسة '{lensName}' موجودة بالفعل ولكن بخصائص مختلفة (Dia, BC, Addtion, Axis).";
                    errors.Add(groupError);

                    // تطبيق الخطأ على جميع السجلات في المجموعة (محسنة)
                    var groupRecords = group.Select(record =>
                    {
                        record.Error = groupError;
                        return record;
                    }).ToList();
                    errorRecords.AddRange(groupRecords);
                    continue;
                }

                if (existingLens == null)
                {
                    added += await CreateNewLens(group, existingData, errors, progress, state, errorRecords);
                }
                else
                {
                    updated += await UpdateExistingLens(existingLens, group, existingData, errors, progress, state, errorRecords);
                }
            }

            progress?.Report(new ProgressReport { Percentage = 90, Message = "جاري حفظ البيانات..." });
            var saveResult = await _context.SaveWithTransactionAndBusy("ImportExcel");

            if (!saveResult.State)
            {
                errors.Add($"خطأ في حفظ البيانات: {saveResult.Message}");
                return (0, 0, errors, errorRecords);
            }

            progress?.Report(new ProgressReport { Percentage = 100, Message = "تم الانتهاء من الاستيراد" });

            return (added, updated, errors, errorRecords);
        }
        catch (Exception ex)
        {
            _context.Reverse();
            errors.Add($"خطأ عام: {ex.Message}");
            return (0, 0, errors, errorRecords);
        }
    }

    /// <summary>
    /// التحقق من صحة بيانات العدسة - يجب أن تحتوي على قيمة واحدة على الأقل من SPH أو CYL أو POW
    /// </summary>
    /// <param name="record">سجل العدسة</param>
    /// <param name="errors">قائمة الأخطاء</param>
    /// <param name="errorRecords">قائمة السجلات التي بها أخطاء</param>
    /// <returns>true إذا كانت البيانات صحيحة، false إذا كانت غير صحيحة</returns>
    private bool ValidateLensData(ExcelRecord record, List<string> errors, List<ExcelRecord> errorRecords)
    {
        // التحقق من وجود قيمة واحدة على الأقل من SPH أو CYL أو POW
        if (!record.SPH.HasValue && !record.CYL.HasValue && !record.Pow.HasValue)
        {
            string errorMessage = $"خطأ: العدسة '{record.Name}' يجب أن تحتوي على قيمة واحدة على الأقل من SPH أو CYL أو POW";
            errors.Add(errorMessage);
            record.Error = errorMessage;
            errorRecords.Add(record);
            return false;
        }
        return true;
    }

    private async Task<int> CreateNewLens(IGrouping<string, ExcelRecord> group, ExistingData existingData,
        List<string> errors, IProgress<ProgressReport> progress, ProgressState state, List<ExcelRecord> errorRecords)
    {
        // الحصول على نوع العدسة من أول سجل في المجموعة
        string lensCategoryName = group.First().LensCategory;
        int? categoryId = null;

        // البحث عن نوع العدسة في قاعدة البيانات
        if (!string.IsNullOrEmpty(lensCategoryName))
        {
            var category = await _context.LensCategories.FirstOrDefaultAsyncWithBusy(c => c.Name == lensCategoryName, "FindLensCategory");
            if (category == null)
            {
                // إنشاء نوع جديد إذا لم يكن موجوداً
                var newCategory = new LensCategory
                {
                    Name = lensCategoryName,
                    CreatedById = CurrentUser.Id,
                    ModifiedById = CurrentUser.Id
                };
                await _context.LensCategories.AddAsyncWithBusy(newCategory);
                var result = await _context.SaveWithTransactionAndBusy("SaveNewLensCategory");
                if (!result.State)
                {
                    errors.Add($"خطأ في حفظ نوع العدسة: {result.Message}");
                    return 0;
                }
                categoryId = newCategory.Id;
            }
            else
            {
                categoryId = category.Id;
            }
        }

        var newLens = new Lens
        {
            Name = group.Key,
            BC = group.First().BC,
            Dia = group.First().Dia,
            Addtion = group.First().Add,
            Axis = group.First().Axis,
            MinimumQuantity = group.First().MinimumQuantity,
            CategoryId = categoryId,
            LensPrescriptions = new List<LensPrescription>()
        };

        foreach (var record in group)
        {
            state.Processed++;
            progress?.Report(CreateProgressReport(state, $"جاري إضافة العدسة: {record.Name}"));

            // التحقق من صحة بيانات العدسة قبل المعالجة
            if (!ValidateLensData(record, errors, errorRecords))
            {
                continue; // تخطي هذا السجل والانتقال للتالي
            }

            short? sphereId = null, cylinderId = null, powId = null;
            if (record.SPH.HasValue)
            {
                sphereId = GetPrescriptionId(record.SPH, existingData, errors);
                if (sphereId == null)
                {
                    string errMsg = $"خطأ: قيمة Sphere '{record.SPH}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }
            if (record.CYL.HasValue)
            {
                cylinderId = GetPrescriptionId(record.CYL, existingData, errors);
                if (cylinderId == null)
                {
                    string errMsg = $"خطأ: قيمة Cylinder '{record.CYL}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }
            if (record.Pow.HasValue)
            {
                powId = GetPrescriptionId(record.Pow, existingData, errors);
                if (powId == null)
                {
                    string errMsg = $"خطأ: قيمة Power '{record.Pow}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }

            var prescription = GetOrCreatePrescription(newLens, record, existingData, errors, sphereId, cylinderId, powId);
            var color = GetOrCreateColor(prescription, record, existingData, errors);

            // إذا كان اللون فارغاً، نضيف السجل إلى قائمة الأخطاء ونتخطى هذا السجل
            if (color == null)
            {
                errorRecords.Add(record);
                continue;
            }

            // تحديث قيمة Barcode في حال عدم التطابق
            if (color.Barcode != (string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode))
            {
                color.Barcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode;
            }

            // تعديل الكميات بحيث نجمع القيم معاً
            UpdateQuantities(color, record.Exp, record.Qte, errors);
        }

        // newLens.Color = newLens.LensPrescriptions.Any(lp => lp.LensPrescriptionColors.Any());
        newLens.Sphere = newLens.LensPrescriptions.Any(lp => lp.SphereId != null);
        newLens.Cylinder = newLens.LensPrescriptions.Any(lp => lp.CylinderId != null);
        newLens.Power = newLens.LensPrescriptions.Any(lp => lp.PowId != null);
        newLens.Exp = newLens.LensPrescriptions.Any(lpc => lpc.LensPrescriptionColors.Any(lpcc => lpcc.LensQuantity.Any(lq => lq.Exp != null)));

        await _context.Lenses.AddAsyncWithBusy(newLens);
        return 1;
    }

    private async Task<int> UpdateExistingLens(Lens existingLens, IGrouping<string, ExcelRecord> group,
        ExistingData existingData, List<string> errors, IProgress<ProgressReport> progress, ProgressState state, List<ExcelRecord> errorRecords)
    {
        int updates = 0;

        // الحصول على نوع العدسة من أول سجل في المجموعة
        string lensCategoryName = group.First().LensCategory;

        // تحديث نوع العدسة إذا كان مختلفاً
        if (!string.IsNullOrEmpty(lensCategoryName))
        {
            var category = await _context.LensCategories.FirstOrDefaultAsyncWithBusy(c => c.Name == lensCategoryName, "FindLensCategoryForUpdate");
            if (category == null)
            {
                // إنشاء نوع جديد إذا لم يكن موجوداً
                var newCategory = new LensCategory
                {
                    Name = lensCategoryName,
                    CreatedById = CurrentUser.Id,
                    ModifiedById = CurrentUser.Id
                };
                await _context.LensCategories.AddAsyncWithBusy(newCategory);
                var result = await _context.SaveWithTransactionAndBusy("SaveNewLensCategoryForUpdate");
                if (!result.State)
                {
                    errors.Add($"خطأ في حفظ نوع العدسة للتحديث: {result.Message}");
                    return 0;
                }
                existingLens.CategoryId = newCategory.Id;
            }
            else if (existingLens.CategoryId != category.Id)
            {
                existingLens.CategoryId = category.Id;
            }
        }

        // تحديث خصائص العدسة من أول سجل في المجموعة
        var firstRecord = group.First();
        existingLens.BC = firstRecord.BC;
        existingLens.Dia = firstRecord.Dia;
        existingLens.Addtion = firstRecord.Add;
        existingLens.Axis = firstRecord.Axis;
        existingLens.MinimumQuantity = firstRecord.MinimumQuantity;

        foreach (var record in group)
        {
            state.Processed++;
            progress?.Report(CreateProgressReport(state, $"جاري تحديث العدسة: {record.Name}"));

            // التحقق من صحة بيانات العدسة قبل المعالجة
            if (!ValidateLensData(record, errors, errorRecords))
            {
                continue; // تخطي هذا السجل والانتقال للتالي
            }

            short? sphereId = null, cylinderId = null, powId = null;
            if (record.SPH.HasValue)
            {
                sphereId = GetPrescriptionId(record.SPH, existingData, errors);
                if (sphereId == null)
                {
                    string errMsg = $"خطأ: قيمة Sphere '{record.SPH}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }
            if (record.CYL.HasValue)
            {
                cylinderId = GetPrescriptionId(record.CYL, existingData, errors);
                if (cylinderId == null)
                {
                    string errMsg = $"خطأ: قيمة Cylinder '{record.CYL}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }
            if (record.Pow.HasValue)
            {
                powId = GetPrescriptionId(record.Pow, existingData, errors);
                if (powId == null)
                {
                    string errMsg = $"خطأ: قيمة Power '{record.Pow}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }

            var prescription = GetOrCreatePrescription(existingLens, record, existingData, errors, sphereId, cylinderId, powId);

            if (prescription.SphereId == sphereId && prescription.CylinderId == cylinderId && prescription.PowId == powId)
            {
                prescription.CostPrice = record.Cost;
                prescription.SellPrice = record.Price;
            }

            var color = GetOrCreateColor(prescription, record, existingData, errors);

            // إذا كان اللون فارغاً، نضيف السجل إلى قائمة الأخطاء ونتخطى هذا السجل
            if (color == null)
            {
                errorRecords.Add(record);
                continue;
            }

            if (color.Barcode != (string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode))
            {
                color.Barcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode;
            }
            updates += UpdateQuantities(color, record.Exp, record.Qte, errors);
        }

        // existingLens.Color = existingLens.LensPrescriptions.Any(lp => lp.LensPrescriptionColors.Any());
        existingLens.Sphere = existingLens.LensPrescriptions.Any(lp => lp.SphereId != null);
        existingLens.Cylinder = existingLens.LensPrescriptions.Any(lp => lp.CylinderId != null);
        existingLens.Power = existingLens.LensPrescriptions.Any(lp => lp.PowId != null);
        existingLens.Exp = existingLens.LensPrescriptions.Any(lpc => lpc.LensPrescriptionColors.Any(lpcc => lpcc.LensQuantity.Any(lq => lq.Exp != null)));
        return updates;
    }

    // تعديل عملية تحديث الكميات بحيث يتم جمع الكمية الجديدة مع الموجودة
    private int UpdateQuantities(LensPrescriptionColor color, DateOnly? exp, int qte, List<string> errors)
    {
        var existingQuantity = color.LensQuantity.FirstOrDefault(q =>
            q.Exp == exp && q.WarehouseId == _warehouseId.Value);
        if (existingQuantity != null)
        {
            existingQuantity.Quantity += qte;
            return 0; // تم التحديث فقط
        }
        else
        {
            color.LensQuantity.Add(new LensQuantity
            {
                Exp = exp,
                Quantity = qte,
                WarehouseId = _warehouseId.Value
            });
            return 1; // تمت الإضافة
        }
    }

    // تعريف الدالة المساعدة لإنشاء أو استرجاع Prescription
    private LensPrescription GetOrCreatePrescription(Lens lens, ExcelRecord record, ExistingData existingData, List<string> errors, short? sphereId, short? cylinderId, short? powId)
    {
        var existingPrescription = lens.LensPrescriptions.FirstOrDefault(lp =>
            lp.SphereId == sphereId &&
            lp.CylinderId == cylinderId &&
            lp.PowId == powId);

        if (existingPrescription != null)
        {
            return existingPrescription;
        }

        var newPrescription = new LensPrescription
        {
            SphereId = sphereId,
            CylinderId = cylinderId,
            PowId = powId,
            CostPrice = record.Cost,
            SellPrice = record.Price,
            LensPrescriptionColors = new List<LensPrescriptionColor>()
        };
        lens.LensPrescriptions.Add(newPrescription);
        return newPrescription;
    }

    // تعريف الدالة المساعدة لإنشاء أو استرجاع Color
    private LensPrescriptionColor GetOrCreateColor(LensPrescription prescription, ExcelRecord record, ExistingData existingData, List<string> errors)
    {
        byte? colorId = null;

        // تنظيف البيانات
        var trimmedHexCode = string.IsNullOrWhiteSpace(record.HexCode) ? string.Empty : record.HexCode.Trim();
        var trimmedColorName = string.IsNullOrWhiteSpace(record.Color) ? string.Empty : record.Color.Trim();

        // الشرط 1: إذا كان الاسم والكود فارغين → استخدام ID = 1 (اللون الافتراضي)
        if (string.IsNullOrEmpty(trimmedColorName) && string.IsNullOrEmpty(trimmedHexCode))
        {
            colorId = 1;
        }
        else
        {
            // البحث المحسن عن لون موجود
            Color existingColor = null;

            // البحث بالكود السادس عشري أولاً
            if (!string.IsNullOrEmpty(trimmedHexCode) && existingData.Colors.TryGetValue(trimmedHexCode, out existingColor))
            {
                colorId = existingColor.Id;
            }
            // البحث بالاسم إذا لم يوجد بالكود
            else if (!string.IsNullOrEmpty(trimmedColorName) && existingData.Colors.TryGetValue(trimmedColorName, out existingColor))
            {
                colorId = existingColor.Id;
            }
            // البحث بالمفتاح المركب
            else if (!string.IsNullOrEmpty(trimmedHexCode) && !string.IsNullOrEmpty(trimmedColorName))
            {
                string compositeKey = $"{trimmedHexCode}|{trimmedColorName}";
                if (existingData.Colors.TryGetValue(compositeKey, out existingColor))
                {
                    colorId = existingColor.Id;
                }
            }

            // الشرط 2: إذا كان الاسم أو الكود موجود → تحديث البيانات وإعطاء ID
            if (existingColor != null)
            {
                colorId = existingColor.Id;
                bool needsUpdate = false;

                // تحديث الكود إذا كان مختلف ومملوء
                if (!string.IsNullOrEmpty(trimmedHexCode) && existingColor.HexCode != trimmedHexCode)
                {
                    existingColor.HexCode = trimmedHexCode;
                    needsUpdate = true;
                }

                // تحديث الاسم إذا كان مختلف ومملوء
                if (!string.IsNullOrEmpty(trimmedColorName) && existingColor.Name != trimmedColorName)
                {
                    existingColor.Name = trimmedColorName;
                    needsUpdate = true;
                }

                // تحديث الألوان الموجودة بدون حفظ فوري
                if (needsUpdate)
                {
                    existingColor.ModifiedById = CurrentUser.Id;
                    existingColor.UpdatedAt = DateTime.Now;
                    _context.Colors.UpdateWithBusy(existingColor);
                    // إزالة الحفظ الفوري - سيتم الحفظ في نهاية العملية
                }

                // إضافة اللون إلى القاموس بالكود الجديد إذا لم يكن موجود
                if (!string.IsNullOrEmpty(trimmedHexCode) && !existingData.Colors.ContainsKey(trimmedHexCode))
                {
                    existingData.Colors[trimmedHexCode] = existingColor;
                }
            }
            // الشرط 3: إذا كان الاسم والكود غير موجودين → 3 حالات فرعية
            else
            {
                Color newColor = null;

                // الحالة 3.1: الاثنان مملوءان → إضافة لون جديد مباشرة
                if (!string.IsNullOrEmpty(trimmedColorName) && !string.IsNullOrEmpty(trimmedHexCode))
                {
                    newColor = new Color
                    {
                        HexCode = trimmedHexCode,
                        Name = trimmedColorName
                    };
                }
                // الحالة 3.2: الكود موجود والاسم فارغ → إضافة اللون بدون اسم
                else if (string.IsNullOrEmpty(trimmedColorName) && !string.IsNullOrEmpty(trimmedHexCode))
                {
                    newColor = new Color
                    {
                        HexCode = trimmedHexCode,
                        Name = string.Empty
                    };
                }
                // الحالة 3.3: الاسم فارغ والكود موجود → وضع الكود في خانة الاسم والكود معاً
                else if (!string.IsNullOrEmpty(trimmedHexCode))
                {
                    newColor = new Color
                    {
                        HexCode = trimmedHexCode,
                        Name = trimmedHexCode
                    };
                }

                // إضافة اللون الجديد إذا تم إنشاؤه
                if (newColor != null)
                {
                    // إضافة الخصائص المطلوبة للون الجديد
                    newColor.CreatedById = CurrentUser.Id;
                    newColor.ModifiedById = CurrentUser.Id;
                    newColor.CreatedAt = DateTime.Now;
                    newColor.UpdatedAt = DateTime.Now;

                    // إضافة اللون للـ Context بدون حفظ فوري
                    _context.Colors.AddWithBusy(newColor);
                    colorId = newColor.Id;

                    // إضافة اللون الجديد إلى القاموس لتجنب التكرار
                    if (!string.IsNullOrEmpty(trimmedHexCode) && !existingData.Colors.ContainsKey(trimmedHexCode))
                    {
                        existingData.Colors[trimmedHexCode] = newColor;
                    }
                    if (!string.IsNullOrEmpty(trimmedColorName) && !existingData.Colors.ContainsKey(trimmedColorName))
                    {
                        existingData.Colors[trimmedColorName] = newColor;
                    }
                }
                else
                {
                    // في حالة فشل إنشاء اللون، استخدام اللون الافتراضي
                    colorId = 1;
                }
            }
        }

        // التأكد من أن colorId ليس null قبل المتابعة
        if (colorId == null)
        {
            string errMsg = $"خطأ: فشل في إنشاء أو العثور على لون للعدسة '{record.Name}'.";
            errors.Add(errMsg);
            record.Error = errMsg;
            return null;
        }

        string normalizedBarcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode;
        var existingLensColor = prescription.LensPrescriptionColors.FirstOrDefault(c => c.ColorId == colorId &&
            ((c.Barcode == normalizedBarcode) ||
             (c.Barcode == null && normalizedBarcode == null)));
        if (existingLensColor != null)
        {
            return existingLensColor;
        }

        var newLensColor = new LensPrescriptionColor
        {
            ColorId = colorId,
            Barcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode,
            LensQuantity = new List<LensQuantity>()
        };
        prescription.LensPrescriptionColors.Add(newLensColor);
        return newLensColor;
    }

    private short? GetPrescriptionId(decimal? value, ExistingData existingData, List<string> errors)
    {
        if (!value.HasValue) return null;
        if (existingData.Prescriptions.TryGetValue(value.Value, out var prescription))
        {
            return prescription.Id;
        }

        errors.Add($"قيمة الوصفة {value} غير موجودة في قاعدة البيانات.");
        return null;
    }

    private decimal? GetDecimal(object value)
    {
        return value == null ? null :
            decimal.TryParse(value.ToString(), out decimal result) ? result : (decimal?)null;
    }

    private DateOnly? GetDate(object value)
    {
        return value == null ? null :
            DateTime.TryParse(value.ToString(), out DateTime result) ?
            DateOnly.FromDateTime(result) : (DateOnly?)null;
    }

    private int GetInt(object value)
    {
        return value == null ? 0 :
            int.TryParse(value.ToString(), out int result) ? result : 0;
    }

    // تعريف الحقول المطلوبة والاختيارية للعدسات
    public Dictionary<string, views.Pages.ProductsContent.FieldInfo> GetRequiredFields()
    {
        return new Dictionary<string, views.Pages.ProductsContent.FieldInfo>
        {
            { "Name", new views.Pages.ProductsContent.FieldInfo { DisplayName = "اسم العدسة", IsRequired = true, PossibleColumnNames = new[] {"اسم العدسة", "إسم العدسة", "اسم الصنف", "إسم الصنف",  "العدسة", "Name", "Lens", "التصنيف" } } },
            { "LensCategory", new views.Pages.ProductsContent.FieldInfo { DisplayName = "نوع العدسة", IsRequired = true, PossibleColumnNames = new[] { "نوع العدسة", "التصنيف", "Category", "Type" } } },
            { "Barcode", new views.Pages.ProductsContent.FieldInfo { DisplayName = "باركود", IsRequired = false, PossibleColumnNames = new[] { "باركود الوحدة", "باركود", "الباركود", "Barcode", "Code" } } },
            { "HexCode", new views.Pages.ProductsContent.FieldInfo { DisplayName = "كود اللون", IsRequired = false, PossibleColumnNames = new[] { "كود اللون", "رمز اللون", "HexCode", "ColorCode" } } },
            { "Color", new views.Pages.ProductsContent.FieldInfo { DisplayName = "اسم اللون", IsRequired = false, PossibleColumnNames = new[] { "إسم اللون", "اسم اللون", "اللون", "Color" } } },
            { "SPH", new views.Pages.ProductsContent.FieldInfo { DisplayName = "SPH", IsRequired = false, PossibleColumnNames = new[] { "SPH", "Sphere" } } },
            { "CYL", new views.Pages.ProductsContent.FieldInfo { DisplayName = "CYL", IsRequired = false, PossibleColumnNames = new[] { "CYL", "Cylinder" } } },
            { "POW", new views.Pages.ProductsContent.FieldInfo { DisplayName = "POW", IsRequired = false, PossibleColumnNames = new[] { "POW", "Power" } } },
            { "BC", new views.Pages.ProductsContent.FieldInfo { DisplayName = "BC", IsRequired = false, PossibleColumnNames = new[] { "BC" } } },
            { "DIA", new views.Pages.ProductsContent.FieldInfo { DisplayName = "DIA", IsRequired = false, PossibleColumnNames = new[] { "DIA", "Dia" } } },
            { "ADD", new views.Pages.ProductsContent.FieldInfo { DisplayName = "ADD", IsRequired = false, PossibleColumnNames = new[] { "ADD", "Add" } } },
            { "AXIS", new views.Pages.ProductsContent.FieldInfo { DisplayName = "AXIS", IsRequired = false, PossibleColumnNames = new[] { "AXIS", "Axis" } } },
            { "Quantity", new views.Pages.ProductsContent.FieldInfo { DisplayName = "الكمية", IsRequired = true, PossibleColumnNames = new[] { "الكمية في العبوة", "الكمية", "كمية", "Quantity", "Qty" } } },
            { "MinimumQuantity", new views.Pages.ProductsContent.FieldInfo { DisplayName = "الحد الأدنى", IsRequired = false, PossibleColumnNames = new[] { "الحد الأدنى", "الحد الادنى", "الحد الأدنى للكمية", "الحد الادنى للكمية", "MinimumQuantity", "MinQty", "MinStock" } } },
            { "Cost", new views.Pages.ProductsContent.FieldInfo { DisplayName = "سعر تكلفة", IsRequired = true, PossibleColumnNames = new[] { "تكلفة العبوة", "تكلفة", "سعر التكلفة", "Cost", "CostPrice" } } },
            { "Price", new views.Pages.ProductsContent.FieldInfo { DisplayName = "سعر بيع", IsRequired = true, PossibleColumnNames = new[] { "سعر بيع الوحدة", "سعر البيع", "سعر", "Price", "SellPrice" } } },
            { "Expiration", new views.Pages.ProductsContent.FieldInfo { DisplayName = "تاريخ الصلاحية", IsRequired = false, PossibleColumnNames = new[] { "له تاريخ صلاحية", "تاريخ الصلاحية", "الصلاحية", "Expiration", "Exp" } } }
        };
    }

    // قراءة بيانات Excel باستخدام تخطيط الأعمدة المخصص
    private async Task<List<ExcelRecord>> ReadExcelData(string filePath)
    {
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        var records = new List<ExcelRecord>();

        using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read);
        using var reader = ExcelReaderFactory.CreateReader(stream);
        var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration
        {
            ConfigureDataTable = _ => new ExcelDataTableConfiguration { UseHeaderRow = true }
        });

        var table = dataSet.Tables[0];

        // الحصول على تخطيط الأعمدة من المستخدم
        var columnMapping = GetColumnMapping(table.Columns);
        if (columnMapping == null || !columnMapping.Any())
        {
            throw new Exception("لم يتم تحديد تخطيط الأعمدة بشكل صحيح");
        }

        foreach (DataRow row in table.Rows)
        {
            try
            {
                var record = new ExcelRecord
                {
                    Name = GetStringValue(row, columnMapping, "Name"),
                    LensCategory = GetStringValue(row, columnMapping, "LensCategory"),
                    Barcode = GetStringValue(row, columnMapping, "Barcode"),
                    HexCode = GetStringValue(row, columnMapping, "HexCode"),
                    Color = GetStringValue(row, columnMapping, "Color"),
                    SPH = columnMapping.ContainsKey("SPH") ? GetDecimal(row[columnMapping["SPH"]]) : null,
                    CYL = columnMapping.ContainsKey("CYL") ? GetDecimal(row[columnMapping["CYL"]]) : null,
                    Pow = columnMapping.ContainsKey("POW") ? GetDecimal(row[columnMapping["POW"]]) : null,
                    BC = columnMapping.ContainsKey("BC") ? GetDecimal(row[columnMapping["BC"]]) : null,
                    Dia = columnMapping.ContainsKey("DIA") ? GetDecimal(row[columnMapping["DIA"]]) : null,
                    Add = columnMapping.ContainsKey("ADD") ? GetDecimal(row[columnMapping["ADD"]]) : null,
                    Axis = columnMapping.ContainsKey("AXIS") ? GetDecimal(row[columnMapping["AXIS"]]) : null,
                    Qte = columnMapping.ContainsKey("Quantity") ? GetInt(row[columnMapping["Quantity"]]) : 0,
                    MinimumQuantity = columnMapping.ContainsKey("MinimumQuantity") ? GetInt(row[columnMapping["MinimumQuantity"]]) : 0,
                    Cost = columnMapping.ContainsKey("Cost") ? GetDecimal(row[columnMapping["Cost"]]) ?? 0m : 0m,
                    Price = columnMapping.ContainsKey("Price") ? GetDecimal(row[columnMapping["Price"]]) ?? 0m : 0m,
                    Exp = columnMapping.ContainsKey("Expiration") ? GetDate(row[columnMapping["Expiration"]]) : null,
                    Error = string.Empty
                };

                // تخطي السجلات التي لا تحتوي على اسم
                if (!string.IsNullOrEmpty(record.Name))
                {
                    records.Add(record);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                // تخطي السجلات التي بها أخطاء
                continue;
            }
        }

        return records;
    }

    // الحصول على قيمة نصية من الصف باستخدام تخطيط الأعمدة
    private string GetStringValue(DataRow row, Dictionary<string, string> columnMapping, string fieldName)
    {
        if (columnMapping.ContainsKey(fieldName))
        {
            return row[columnMapping[fieldName]]?.ToString() ?? string.Empty;
        }
        return string.Empty;
    }

    // الحصول على تخطيط الأعمدة
    private Dictionary<string, string> GetColumnMapping(DataColumnCollection columns)
    {
        // إذا تم تعيين تخطيط أعمدة مخصص، استخدمه
        if (_customColumnMapping != null && _customColumnMapping.Any())
        {
            // التحقق من وجود الأعمدة المطلوبة (محسنة)
            var missingColumns = _customColumnMapping
                .Where(mapping => !columns.Contains(mapping.Value))
                .Select(mapping => mapping.Value)
                .ToList();

            if (missingColumns.Any())
            {
                System.Diagnostics.Debug.WriteLine($"Missing columns: {string.Join(", ", missingColumns)}");
            }

            return _customColumnMapping;
        }

        // في حالة عدم وجود تخطيط مخصص، نستخدم التخطيط الافتراضي
        var defaultMapping = new Dictionary<string, string>
        {
            { "Name", "اسم العدسة" },
            { "LensCategory", "نوع العدسة" },
            { "Barcode", "باركود الوحدة" },
            { "HexCode", "كود اللون" },
            { "Color", "إسم اللون" },
            { "SPH", "SPH" },
            { "CYL", "CYL" },
            { "POW", "POW" },
            { "BC", "BC" },
            { "DIA", "DIA" },
            { "ADD", "ADD" },
            { "AXIS", "AXIS" },
            { "Quantity", "الكمية في العبوة" },
            { "MinimumQuantity", "الحد الأدنى" },
            { "Cost", "تكلفة العبوة" },
            { "Price", "سعر بيع الوحدة" },
            { "Expiration", "له تاريخ صلاحية" }
        };

        // التحقق من وجود الأعمدة المطلوبة (محسنة)
        var missingDefaultColumns = defaultMapping
            .Where(mapping => !columns.Contains(mapping.Value))
            .Select(mapping => mapping.Value)
            .ToList();

        if (missingDefaultColumns.Any())
        {
            System.Diagnostics.Debug.WriteLine($"Missing default columns: {string.Join(", ", missingDefaultColumns)}");
        }

        return defaultMapping;
    }

    private async Task<ExistingData> GetExistingData(List<ExcelRecord> records)
    {
        var lensNames = records.Select(r => r.Name).Distinct().ToList();
        var hexCodes = records.Select(r => r.HexCode).Where(h => !string.IsNullOrEmpty(h)).Distinct().ToList();
        var colorNames = records.Select(r => r.Color).Where(c => !string.IsNullOrEmpty(c)).Distinct().ToList();
        var prescriptionValues = records
            .SelectMany(r => new[] { r.SPH, r.CYL, r.Pow })
            .Where(v => v.HasValue)
            .Select(v => v.Value)
            .Distinct()
            .ToList();

        // جلب جميع الوصفات وتجميعها حسب القيمة لتجنب المفاتيح المكررة
        var prescriptionsList = await _context.Prescriptions
            .Where(p => prescriptionValues.Contains(p.Value))
            .ToListAsyncWithBusy("LoadExistingPrescriptions");

        // إنشاء Dictionary مع أخذ أول سجل لكل قيمة في حالة وجود مكررات
        var prescriptionsDict = prescriptionsList
            .GroupBy(p => p.Value)
            .ToDictionary(g => g.Key, g => g.First());

        // جلب الألوان بالكود السادس عشري أو الاسم
        var colorsList = await _context.Colors
            .Where(c => hexCodes.Contains(c.HexCode) || colorNames.Contains(c.Name))
            .ToListAsyncWithBusy("LoadExistingColors");

        // إنشاء Dictionary محسن يدعم البحث بالكود والاسم
        var colorsDict = new Dictionary<string, Color>();

        foreach (var color in colorsList)
        {
            // إضافة اللون بالكود السادس عشري إذا كان موجود وغير مكرر
            if (!string.IsNullOrEmpty(color.HexCode) && !colorsDict.ContainsKey(color.HexCode))
            {
                colorsDict[color.HexCode] = color;
            }

            // إضافة اللون بالاسم إذا كان موجود وغير مكرر
            if (!string.IsNullOrEmpty(color.Name) && !colorsDict.ContainsKey(color.Name))
            {
                colorsDict[color.Name] = color;
            }

            // إضافة اللون بمفتاح مركب للبحث المزدوج
            string compositeKey = $"{color.HexCode}|{color.Name}";
            if (!colorsDict.ContainsKey(compositeKey))
            {
                colorsDict[compositeKey] = color;
            }
        }

        return new ExistingData
        {
            Lenses = await _context.Lenses
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
                .ThenInclude(lpc => lpc.LensQuantity)
                .Where(l => lensNames.Contains(l.Name))
                .ToDictionaryAsyncWithBusy(l => l.Name, "LoadExistingLenses"),
            Colors = colorsDict,
            Prescriptions = prescriptionsDict
        };
    }

    // دالة إنشاء تقرير التقدم مع رسالة توضيحية تشمل نوع العملية وعدد السجلات المعالجة والنسبة المئوية
    private ProgressReport CreateProgressReport(ProgressState state, string action)
    {
        int percentage = (int)((state.Processed * 100.0) / state.Total);
        string message = $"{action} - تمت معالجة {state.Processed} من {state.Total} سجل. نسبة التقدم: {percentage}%";
        return new ProgressReport
        {
            Percentage = percentage,
            Message = message
        };
    }

    // الدالة لإنشاء ملف Excel يحتوي على السجلات التي بها أخطاء مع عمود "Error Reason"
    public void ExportErrorExcel(List<ExcelRecord> errorRecords, string filePath)
    {
        var workbook = new XLWorkbook();
        var worksheet = workbook.Worksheets.Add("Errors");
        // عناوين الأعمدة
        worksheet.Cell(1, 1).Value = "الاسم";
        worksheet.Cell(1, 2).Value = "الباركود";
        worksheet.Cell(1, 3).Value = "رمز اللون";
        worksheet.Cell(1, 4).Value = "اسم اللون";
        worksheet.Cell(1, 5).Value = "SPH";
        worksheet.Cell(1, 6).Value = "CYL";
        worksheet.Cell(1, 7).Value = "Pow";
        worksheet.Cell(1, 8).Value = "BC";
        worksheet.Cell(1, 9).Value = "Dia";
        worksheet.Cell(1, 10).Value = "Add";
        worksheet.Cell(1, 11).Value = "Axis";
        worksheet.Cell(1, 12).Value = "الكمية";
        worksheet.Cell(1, 13).Value = "الحد الأدنى";
        worksheet.Cell(1, 14).Value = "سعر التكلفة";
        worksheet.Cell(1, 15).Value = "سعر البيع";
        worksheet.Cell(1, 16).Value = "الصلاحية";
        worksheet.Cell(1, 17).Value = "نوع العدسة";
        worksheet.Cell(1, 18).Value = "سبب الخطا";

        int row = 2;
        foreach (var record in errorRecords)
        {
            worksheet.Cell(row, 1).Value = record.Name;
            worksheet.Cell(row, 2).Value = record.Barcode;
            worksheet.Cell(row, 3).Value = record.HexCode;
            worksheet.Cell(row, 4).Value = record.Color;
            worksheet.Cell(row, 5).Value = record.SPH;
            worksheet.Cell(row, 6).Value = record.CYL;
            worksheet.Cell(row, 7).Value = record.Pow;
            worksheet.Cell(row, 8).Value = record.BC;
            worksheet.Cell(row, 9).Value = record.Dia;
            worksheet.Cell(row, 10).Value = record.Add;
            worksheet.Cell(row, 11).Value = record.Axis;
            worksheet.Cell(row, 12).Value = record.Qte;
            worksheet.Cell(row, 13).Value = record.MinimumQuantity;
            worksheet.Cell(row, 14).Value = record.Cost;
            worksheet.Cell(row, 15).Value = record.Price;
            worksheet.Cell(row, 16).Value = record.Exp?.ToString("yyyy-MM-dd") ?? "";
            worksheet.Cell(row, 17).Value = record.LensCategory;
            worksheet.Cell(row, 18).Value = record.Error;
            row++;
        }

        // Auto-fit columns for better readability
        worksheet.Columns().AdjustToContents();

        // If the path is a directory, create a file name with timestamp
        string errorFilePath;
        if (Directory.Exists(filePath))
        {
            errorFilePath = Path.Combine(filePath, $"LensImportErrors_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
        }
        else
        {
            // If it's already a file path, use it directly
            errorFilePath = filePath;
        }

        workbook.SaveAs(errorFilePath);
    }

    // تعريف الكلاس الخاص بسجل الإكسل مع إضافة خاصية Error لتخزين سبب الخطأ
    public class ExcelRecord
    {
        public string Name { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty;
        public string HexCode { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public decimal? SPH { get; set; }
        public decimal? CYL { get; set; }
        public decimal? Pow { get; set; }
        public decimal? BC { get; set; }
        public decimal? Dia { get; set; }
        public decimal? Add { get; set; }
        public decimal? Axis { get; set; }
        public int Qte { get; set; }
        public int MinimumQuantity { get; set; } = 0;
        public decimal Cost { get; set; }
        public decimal Price { get; set; }
        public DateOnly? Exp { get; set; }
        public string Error { get; set; } = string.Empty;
        // إضافة حقل لنوع العدسة
        public string LensCategory { get; set; } = string.Empty;
    }

    private class ExistingData
    {
        public Dictionary<string, Lens> Lenses { get; set; } = new Dictionary<string, Lens>();
        public Dictionary<string, Color> Colors { get; set; } = new Dictionary<string, Color>();
        public Dictionary<decimal, Prescription> Prescriptions { get; set; } = new Dictionary<decimal, Prescription>();
    }

    private class ProgressState
    {
        public int Processed { get; set; }
        public int Total { get; set; }
    }

    public class ProgressReport
    {
        public int Percentage { get; set; }
        public string Message { get; set; }
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context?.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~ExcelLensService()
    {
        Dispose(false);
    }
}
