using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using VisionPoint.UI.Helper.ImportExport;
using VisionPoint.UI.PL;
using static VisionPoint.UI.PL.ExcelLensService;

namespace VisionPoint.UI.Tests
{
    /// <summary>
    /// اختبارات وحدة لنظام استيراد العدسات
    /// </summary>
    [TestClass]
    public class LensImportTests
    {
        /// <summary>
        /// اختبار تصنيف الأخطاء
        /// </summary>
        [TestMethod]
        public void TestErrorCategorization()
        {
            // ترتيب البيانات
            var errors = new List<string>
            {
                "خطأ: قيمة Sphere '2.50' غير موجودة في جدول المقاسات",
                "تم العثور على ألوان مكررة: أحمر - #FF0000",
                "تم العثور على 5 كمية بدون ربط صحيح بلون العدسة",
                "خطأ: فئة العدسة 'عدسات طبية' مكررة",
                "خطأ عام في النظام"
            };

            // تنفيذ الاختبار
            var categorizedErrors = LensImportErrorHandler.CategorizeErrors(errors);

            // التحقق من النتائج
            Assert.IsTrue(categorizedErrors.ContainsKey("أخطاء البيانات"));
            Assert.IsTrue(categorizedErrors.ContainsKey("أخطاء الألوان"));
            Assert.IsTrue(categorizedErrors.ContainsKey("أخطاء الكميات"));
            Assert.IsTrue(categorizedErrors.ContainsKey("أخطاء الفئات"));
            Assert.IsTrue(categorizedErrors.ContainsKey("أخطاء عامة"));

            Assert.AreEqual(1, categorizedErrors["أخطاء البيانات"].Count);
            Assert.AreEqual(1, categorizedErrors["أخطاء الألوان"].Count);
            Assert.AreEqual(1, categorizedErrors["أخطاء الكميات"].Count);
            Assert.AreEqual(1, categorizedErrors["أخطاء الفئات"].Count);
            Assert.AreEqual(1, categorizedErrors["أخطاء عامة"].Count);
        }

        /// <summary>
        /// اختبار إنشاء تقرير الأخطاء
        /// </summary>
        [TestMethod]
        public void TestErrorReportGeneration()
        {
            // ترتيب البيانات
            var errors = new List<string>
            {
                "خطأ في البيانات",
                "خطأ في الألوان"
            };

            var errorRecords = new List<ExcelRecord>
            {
                new ExcelRecord
                {
                    Name = "عدسة تجريبية",
                    Error = "خطأ في البيانات",
                    SPH = 2.5m,
                    CYL = -1.0m,
                    Pow = null
                }
            };

            // تنفيذ الاختبار
            var report = LensImportErrorHandler.GenerateErrorReport(errors, errorRecords);

            // التحقق من النتائج
            Assert.IsTrue(report.Contains("تقرير أخطاء استيراد العدسات"));
            Assert.IsTrue(report.Contains("إجمالي الأخطاء: 2"));
            Assert.IsTrue(report.Contains("السجلات المرفوضة: 1"));
            Assert.IsTrue(report.Contains("عدسة تجريبية"));
        }

        /// <summary>
        /// اختبار اقتراح الحلول
        /// </summary>
        [TestMethod]
        public void TestSolutionSuggestions()
        {
            // ترتيب البيانات
            var errors = new List<string>
            {
                "تم العثور على ألوان مكررة",
                "قيمة Sphere غير موجودة في جدول المقاسات",
                "كمية بدون ربط صحيح",
                "لون غير صحيح"
            };

            // تنفيذ الاختبار
            var suggestions = LensImportErrorHandler.SuggestSolutions(errors);

            // التحقق من النتائج
            Assert.IsTrue(suggestions.Any(s => s.Contains("تأكد من عدم وجود بيانات مكررة")));
            Assert.IsTrue(suggestions.Any(s => s.Contains("تأكد من إضافة جميع المقاسات المطلوبة")));
            Assert.IsTrue(suggestions.Any(s => s.Contains("تأكد من تحديد المخزن الصحيح")));
            Assert.IsTrue(suggestions.Any(s => s.Contains("تأكد من صحة أكواد الألوان")));
        }

        /// <summary>
        /// اختبار حالة عدم وجود أخطاء
        /// </summary>
        [TestMethod]
        public void TestNoErrors()
        {
            // ترتيب البيانات
            var errors = new List<string>();
            var errorRecords = new List<ExcelRecord>();

            // تنفيذ الاختبار
            var categorizedErrors = LensImportErrorHandler.CategorizeErrors(errors);
            var suggestions = LensImportErrorHandler.SuggestSolutions(errors);

            // التحقق من النتائج
            Assert.AreEqual(0, categorizedErrors.Count);
            Assert.AreEqual(0, suggestions.Count);
        }

        /// <summary>
        /// اختبار حالة أخطاء متعددة من نفس النوع
        /// </summary>
        [TestMethod]
        public void TestMultipleErrorsOfSameType()
        {
            // ترتيب البيانات
            var errors = new List<string>
            {
                "لون أحمر مكرر",
                "لون أزرق مكرر",
                "لون أخضر مكرر"
            };

            // تنفيذ الاختبار
            var categorizedErrors = LensImportErrorHandler.CategorizeErrors(errors);

            // التحقق من النتائج
            Assert.AreEqual(1, categorizedErrors.Count);
            Assert.IsTrue(categorizedErrors.ContainsKey("أخطاء الألوان"));
            Assert.AreEqual(3, categorizedErrors["أخطاء الألوان"].Count);
        }
    }
}
